<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试页面 - Vision Sniffer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #007bff;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-section h2 {
            color: #007bff;
            margin-top: 0;
        }
        
        .image-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        
        .image-item {
            flex: 1;
            min-width: 200px;
            text-align: center;
        }
        
        .image-item img {
            max-width: 100%;
            height: 150px;
            object-fit: cover;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        
        .video-container {
            margin: 20px 0;
        }
        
        .video-container video {
            width: 100%;
            max-width: 500px;
            height: 300px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        
        .background-demo {
            height: 200px;
            background-size: cover;
            background-position: center;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            margin: 20px 0;
        }
        
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .instructions h3 {
            color: #856404;
            margin-top: 0;
        }
        
        .debug-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔍 Vision Sniffer 简单测试页面</h1>
    
    <div class="instructions">
        <h3>📋 测试说明</h3>
        <ol>
            <li>确保已安装 Vision Sniffer 插件</li>
            <li>点击浏览器工具栏中的插件图标</li>
            <li>查看是否能识别到下面的媒体文件</li>
            <li>尝试点击"刷新"按钮重新扫描</li>
            <li>打开浏览器开发者工具查看控制台日志</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>📷 图片测试</h2>
        <p>以下是一些测试图片，应该能被插件识别到：</p>
        <div class="image-container">
            <div class="image-item">
                <img src="https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=Test+Image+1" alt="测试图片1">
                <p>测试图片 1</p>
            </div>
            <div class="image-item">
                <img src="https://via.placeholder.com/300x200/4ECDC4/FFFFFF?text=Test+Image+2" alt="测试图片2">
                <p>测试图片 2</p>
            </div>
            <div class="image-item">
                <img src="https://via.placeholder.com/300x200/45B7D1/FFFFFF?text=Test+Image+3" alt="测试图片3">
                <p>测试图片 3</p>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🎥 视频测试</h2>
        <p>以下是一个测试视频：</p>
        <div class="video-container">
            <video controls poster="https://via.placeholder.com/500x300/96CEB4/FFFFFF?text=Video+Poster">
                <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">
                <source src="https://www.w3schools.com/html/mov_bbb.ogg" type="video/ogg">
                您的浏览器不支持视频播放。
            </video>
        </div>
    </div>

    <div class="test-section">
        <h2>🖼️ 背景图片测试</h2>
        <p>以下元素使用了CSS背景图片：</p>
        <div class="background-demo" style="background-image: url('https://via.placeholder.com/800x200/FF9F43/FFFFFF?text=Background+Image');">
            CSS 背景图片
        </div>
    </div>

    <div class="debug-info">
        <h3>🐛 调试信息</h3>
        <p><strong>页面URL:</strong> <span id="pageUrl"></span></p>
        <p><strong>图片数量:</strong> <span id="imageCount">0</span></p>
        <p><strong>视频数量:</strong> <span id="videoCount">0</span></p>
        <p><strong>背景图片数量:</strong> <span id="backgroundCount">0</span></p>
        <p><strong>页面加载时间:</strong> <span id="loadTime"></span></p>
    </div>

    <script>
        // 更新调试信息
        function updateDebugInfo() {
            document.getElementById('pageUrl').textContent = window.location.href;
            document.getElementById('imageCount').textContent = document.querySelectorAll('img').length;
            document.getElementById('videoCount').textContent = document.querySelectorAll('video').length;
            
            // 计算背景图片数量
            let backgroundCount = 0;
            document.querySelectorAll('*').forEach(element => {
                const style = window.getComputedStyle(element);
                if (style.backgroundImage && style.backgroundImage !== 'none') {
                    backgroundCount++;
                }
            });
            document.getElementById('backgroundCount').textContent = backgroundCount;
            
            document.getElementById('loadTime').textContent = new Date().toLocaleTimeString();
        }

        // 页面加载完成后更新信息
        document.addEventListener('DOMContentLoaded', updateDebugInfo);
        
        // 添加一些控制台日志
        console.log('简单测试页面已加载');
        console.log('图片元素:', document.querySelectorAll('img'));
        console.log('视频元素:', document.querySelectorAll('video'));
        
        // 模拟动态添加内容
        setTimeout(() => {
            console.log('添加动态内容...');
            const dynamicImg = document.createElement('img');
            dynamicImg.src = 'https://via.placeholder.com/200x150/E17055/FFFFFF?text=Dynamic+Image';
            dynamicImg.alt = '动态添加的图片';
            dynamicImg.style.cssText = 'margin: 10px; border: 2px solid #E17055; border-radius: 8px;';
            
            const container = document.querySelector('.test-section');
            container.appendChild(dynamicImg);
            
            updateDebugInfo();
            console.log('动态内容添加完成');
        }, 2000);
    </script>
</body>
</html>
