# Vision Sniffer - 媒体文件识别器

一个强大的Chrome浏览器插件，能够自动识别和提取网页中的所有图片和视频文件。

## 功能特性

### 🔍 智能识别
- **图片识别**: 自动识别网页中的所有图片元素（img标签）
- **视频识别**: 识别video标签及其source元素
- **背景图片**: 检测CSS背景图片
- **iframe内容**: 扫描同源iframe中的媒体文件
- **动态内容**: 实时监控新加载的媒体内容

### 📊 详细信息
- 显示文件类型、尺寸、大小
- 显示来源域名和完整URL
- 提供缩略图预览（可选）
- 统计各类型文件数量

### 🔧 强大过滤
- **类型过滤**: 按图片、视频、背景图分类
- **大小过滤**: 按文件大小范围筛选
- **搜索功能**: 支持URL和域名搜索
- **最小大小**: 设置最小文件大小阈值

### 📥 批量下载
- 单个文件下载
- 批量选择下载
- 自动生成文件名
- 支持多种格式

### 🎯 便捷操作
- 页面内高亮显示
- 一键复制链接
- 按域名分组显示
- 实时扫描开关

## 安装方法

### 方法一：开发者模式安装
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择插件文件夹

### 方法二：生成图标（可选）
1. 在浏览器中打开 `generate_icons.html`
2. 点击"Download All Icons"按钮
3. 将下载的PNG文件放入 `icons/` 文件夹

## 使用方法

### 基本使用
1. 安装插件后，浏览器工具栏会出现Vision Sniffer图标
2. 访问任意网页
3. 点击插件图标打开媒体文件列表
4. 查看识别到的所有媒体文件

### 过滤和搜索
- 使用类型下拉菜单筛选特定类型的文件
- 使用大小过滤器按文件大小筛选
- 在搜索框中输入关键词搜索URL或域名

### 下载文件
- 点击单个文件的下载按钮下载
- 勾选多个文件后点击"下载选中"批量下载
- 使用"全选"/"取消全选"快速选择

### 高级功能
- 点击"🎯"按钮在页面中高亮显示对应元素
- 点击"📋"按钮复制文件链接到剪贴板
- 在设置中开启按域名分组显示

## 设置选项

### 扫描设置
- **自动扫描**: 自动检测新加载的媒体内容
- **显示缩略图**: 在列表中显示图片缩略图
- **按域名分组**: 将文件按来源域名分组显示

### 过滤设置
- **最小文件大小**: 设置最小文件大小阈值（KB）
- 只显示大于指定大小的文件

## 技术特性

### 兼容性
- 支持Chrome Manifest V3
- 兼容最新版Chrome浏览器
- 支持各种图片格式（JPG, PNG, GIF, WebP, SVG等）
- 支持各种视频格式（MP4, WebM, OGG等）

### 性能优化
- 智能去重，避免重复识别
- 延迟加载，减少性能影响
- 内存优化，及时清理无用数据

### 安全性
- 仅访问当前标签页内容
- 不收集用户隐私数据
- 本地存储设置信息

## 文件结构

```
visionSniffer/
├── manifest.json          # 插件配置文件
├── content.js             # 内容脚本，负责扫描网页
├── background.js          # 后台脚本，处理下载等任务
├── popup.html            # 弹出窗口HTML
├── popup.css             # 弹出窗口样式
├── popup.js              # 弹出窗口逻辑
├── icons/                # 插件图标文件夹
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
├── generate_icons.html   # 图标生成工具
└── README.md            # 说明文档
```

## 开发说明

### 核心组件
- **MediaSniffer类**: 负责扫描和识别媒体文件
- **PopupManager类**: 管理弹出窗口界面和交互
- **BackgroundService类**: 处理下载和后台任务

### 扩展功能
插件采用模块化设计，可以轻松扩展新功能：
- 添加新的媒体类型识别
- 集成云存储服务
- 添加图片编辑功能
- 支持更多文件格式

## 常见问题

### Q: 为什么某些图片无法识别？
A: 可能是跨域限制或动态加载的内容，尝试刷新扫描。

### Q: 下载的文件名为什么是随机的？
A: 当无法从URL获取文件名时，会自动生成带时间戳的文件名。

### Q: 插件会影响网页性能吗？
A: 插件经过性能优化，对网页性能影响极小。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持图片和视频识别
- 实现基本的过滤和下载功能
- 添加设置面板

## 许可证

MIT License - 详见LICENSE文件

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
