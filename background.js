// Vision Sniffer Background Script
// 处理下载和后台任务

class BackgroundService {
  constructor() {
    this.setupMessageListener();
    this.setupDownloadListener();
  }

  // 设置消息监听器
  setupMessageListener() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      switch (request.action) {
        case 'downloadMedia':
          this.downloadMedia(request.url, request.filename)
            .then(downloadId => sendResponse({ success: true, downloadId }))
            .catch(error => sendResponse({ success: false, error: error.message }));
          return true;

        case 'downloadBatch':
          this.downloadBatch(request.items)
            .then(results => sendResponse({ success: true, results }))
            .catch(error => sendResponse({ success: false, error: error.message }));
          return true;

        case 'getMediaInfo':
          this.getMediaInfo(request.url)
            .then(info => sendResponse({ success: true, info }))
            .catch(error => sendResponse({ success: false, error: error.message }));
          return true;
      }
    });
  }

  // 设置下载监听器
  setupDownloadListener() {
    chrome.downloads.onChanged.addListener((downloadDelta) => {
      if (downloadDelta.state && downloadDelta.state.current === 'complete') {
        console.log('Download completed:', downloadDelta.id);
      }
    });
  }

  // 下载单个媒体文件
  async downloadMedia(url, filename) {
    try {
      const downloadId = await chrome.downloads.download({
        url: url,
        filename: filename || this.generateFilename(url),
        saveAs: false
      });
      return downloadId;
    } catch (error) {
      console.error('Download failed:', error);
      throw error;
    }
  }

  // 批量下载媒体文件
  async downloadBatch(items) {
    const results = [];
    
    for (const item of items) {
      try {
        const downloadId = await this.downloadMedia(item.url, item.filename);
        results.push({ url: item.url, success: true, downloadId });
        
        // 添加延迟避免过快的下载请求
        await this.delay(500);
      } catch (error) {
        results.push({ url: item.url, success: false, error: error.message });
      }
    }
    
    return results;
  }

  // 获取媒体文件信息
  async getMediaInfo(url) {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      const contentLength = response.headers.get('content-length');
      const contentType = response.headers.get('content-type');
      const lastModified = response.headers.get('last-modified');
      
      return {
        size: contentLength ? parseInt(contentLength) : null,
        type: contentType,
        lastModified: lastModified ? new Date(lastModified) : null,
        url: url
      };
    } catch (error) {
      console.error('Failed to get media info:', error);
      throw error;
    }
  }

  // 生成文件名
  generateFilename(url) {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      const filename = pathname.split('/').pop();
      
      if (filename && filename.includes('.')) {
        return `vision-sniffer/${filename}`;
      } else {
        const extension = this.guessExtension(url);
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        return `vision-sniffer/media_${timestamp}${extension}`;
      }
    } catch (error) {
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      return `vision-sniffer/media_${timestamp}`;
    }
  }

  // 猜测文件扩展名
  guessExtension(url) {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp'];
    const videoExtensions = ['.mp4', '.webm', '.ogg', '.avi', '.mov', '.wmv', '.flv'];
    
    const lowerUrl = url.toLowerCase();
    
    for (const ext of imageExtensions) {
      if (lowerUrl.includes(ext)) return ext;
    }
    
    for (const ext of videoExtensions) {
      if (lowerUrl.includes(ext)) return ext;
    }
    
    return '.unknown';
  }

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 初始化后台服务
const backgroundService = new BackgroundService();

// 插件安装时的处理
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === 'install') {
    console.log('Vision Sniffer installed successfully!');
  } else if (details.reason === 'update') {
    console.log('Vision Sniffer updated to version', chrome.runtime.getManifest().version);
  }
});
