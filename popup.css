/* Vision Sniffer Popup Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 400px;
  min-height: 500px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  color: #333;
  background: #f8f9fa;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 600px;
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  width: 24px;
  height: 24px;
}

.logo h1 {
  font-size: 18px;
  font-weight: 600;
}

.actions {
  display: flex;
  gap: 8px;
}

/* 统计信息 */
.stats {
  display: flex;
  justify-content: space-around;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e9ecef;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #6c757d;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  margin-left: 4px;
}

/* 过滤器 */
.filters {
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 12px;
  color: #6c757d;
  min-width: 40px;
}

.filter-group select,
.filter-group input {
  flex: 1;
  padding: 4px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 12px;
}

/* 批量操作 */
.batch-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.selected-info {
  margin-left: auto;
  font-size: 12px;
  color: #6c757d;
}

/* 媒体列表 */
.media-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
  max-height: 400px;
}

.media-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  margin-bottom: 8px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  position: relative;
}

.media-item:hover {
  box-shadow: 0 6px 20px rgba(0,0,0,0.12);
  transform: translateY(-2px);
  border-color: #007bff;
}

.media-item.selected {
  border: 2px solid #007bff;
  background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
  box-shadow: 0 4px 16px rgba(0,123,255,0.2);
}

.media-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #007bff;
}

.media-thumbnail {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  object-fit: cover;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  transition: all 0.2s ease;
}

.media-thumbnail:hover {
  border-color: #007bff;
  transform: scale(1.05);
}

.media-info {
  flex: 1;
  min-width: 0;
}

.media-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 6px;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.media-details {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 6px;
}

.media-details span {
  background: #f8f9fa;
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.media-url {
  font-size: 11px;
  color: #adb5bd;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: monospace;
}

.media-actions {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.type-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 16px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.type-badge.image {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1565c0;
  border: 1px solid #90caf9;
}

.type-badge.video {
  background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
  color: #ad1457;
  border: 1px solid #f48fb1;
}

.type-badge.background-image {
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
  color: #6a1b9a;
  border: 1px solid #ce93d8;
}

/* 按钮样式 */
.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #1e7e34;
}

.btn-outline {
  background: transparent;
  border: 1px solid #ced4da;
  color: #6c757d;
}

.btn-outline:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.btn-small {
  padding: 6px 12px;
  font-size: 11px;
  min-width: 60px;
  border-radius: 6px;
}

.btn-icon {
  padding: 8px;
  background: rgba(255,255,255,0.2);
  color: white;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon:hover {
  background: rgba(255,255,255,0.3);
  transform: scale(1.1);
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #6c757d;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

/* 底部信息 */
.footer {
  padding: 12px 16px;
  background: white;
  border-top: 1px solid #e9ecef;
}

.page-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.page-url {
  font-size: 11px;
  color: #6c757d;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 设置面板 */
.settings-panel {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  z-index: 1000;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease-in-out;
}

.settings-panel.show {
  opacity: 1;
  transform: translateX(0);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.settings-header h3 {
  margin: 0;
  color: #495057;
  font-size: 18px;
}

.settings-header .btn-icon {
  background: #dc3545;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.settings-header .btn-icon:hover {
  background: #c82333;
  transform: scale(1.1);
}

.settings-content {
  padding: 16px;
}

.setting-item {
  margin-bottom: 16px;
}

.setting-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.setting-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

.setting-item input[type="number"] {
  width: 80px;
  padding: 4px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  margin-left: 8px;
}

/* 域名分组样式 */
.domain-group {
  margin-bottom: 16px;
}

.domain-header {
  background: #f8f9fa;
  padding: 8px 12px;
  font-weight: 600;
  font-size: 13px;
  color: #495057;
  border-radius: 4px 4px 0 0;
  border-bottom: 1px solid #e9ecef;
  position: sticky;
  top: 0;
  z-index: 10;
}

.domain-group .media-item {
  border-radius: 0;
  margin-bottom: 1px;
}

.domain-group .media-item:last-child {
  border-radius: 0 0 8px 8px;
}

/* Toast 通知样式 */
.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #333;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 10000;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.toast.show {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 450px) {
  body {
    width: 350px;
  }

  .filters {
    flex-direction: column;
  }

  .batch-actions {
    flex-wrap: wrap;
  }

  .media-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .media-actions {
    align-self: flex-end;
    margin-top: 8px;
  }
}

/* 滚动条样式 */
.media-list::-webkit-scrollbar {
  width: 6px;
}

.media-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.media-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.media-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
.media-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态改进 */
.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
