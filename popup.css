/* Vision Sniffer Popup Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 420px;
  height: 600px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
  font-size: 14px;
  color: #2c3e50;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
  border-radius: 12px;
  margin: 8px;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3);
  overflow: hidden;
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
}

.header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 28px;
  height: 28px;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

.logo h1 {
  font-size: 20px;
  font-weight: 700;
  letter-spacing: -0.5px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.actions {
  display: flex;
  gap: 8px;
}

/* 统计信息 */
.stats {
  display: flex;
  justify-content: space-around;
  padding: 12px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  flex-shrink: 0;
}

.stat-item {
  text-align: center;
  padding: 6px 10px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.08);
  min-width: 50px;
  transition: all 0.2s ease;
}

.stat-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 12px rgba(0,0,0,0.12);
}

.stat-label {
  font-size: 10px;
  color: #6c757d;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 16px;
  font-weight: 700;
  color: #2c3e50;
  margin-top: 2px;
  display: block;
}

/* 过滤器 */
.filters {
  padding: 12px 20px;
  background: white;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-group label {
  font-size: 11px;
  color: #495057;
  font-weight: 600;
  min-width: 45px;
}

.filter-group select,
.filter-group input {
  flex: 1;
  padding: 6px 10px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 12px;
  background: #f8f9fa;
  transition: all 0.2s ease;
}

.filter-group select:focus,
.filter-group input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

/* 批量操作 */
.batch-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  flex-shrink: 0;
}

.selected-info {
  margin-left: auto;
  font-size: 11px;
  color: #495057;
  font-weight: 600;
  padding: 4px 10px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.08);
}

/* 媒体列表 */
.media-list {
  flex: 1;
  overflow-y: auto;
  padding: 12px 20px;
  background: #f8f9fa;
  min-height: 0; /* 重要：允许flex子项收缩 */
}

.media-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  margin-bottom: 8px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.media-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.media-item:hover {
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  transform: translateY(-4px);
  border-color: #667eea;
}

.media-item:hover::before {
  transform: scaleX(1);
}

.media-item.selected {
  border: 2px solid #667eea;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.25);
}

.media-item.selected::before {
  transform: scaleX(1);
}

.media-checkbox {
  width: 20px;
  height: 20px;
  cursor: pointer;
  accent-color: #667eea;
  border-radius: 4px;
}

.media-thumbnail {
  width: 56px;
  height: 56px;
  border-radius: 8px;
  object-fit: cover;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid #e9ecef;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  flex-shrink: 0;
}

.media-thumbnail:hover {
  border-color: #667eea;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.media-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.media-title {
  font-weight: 600;
  font-size: 13px;
  margin-bottom: 4px;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 6px;
  line-height: 1.2;
}

.media-details {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  font-size: 11px;
  color: #6c757d;
  margin-bottom: 4px;
}

.media-details span {
  background: #f8f9fa;
  padding: 1px 6px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  font-size: 10px;
}

.media-url {
  font-size: 10px;
  color: #adb5bd;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: monospace;
  line-height: 1.2;
}

.media-actions {
  display: flex;
  flex-direction: row;
  gap: 4px;
  align-items: center;
  flex-shrink: 0;
}

.type-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 16px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.type-badge.image {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1565c0;
  border: 1px solid #90caf9;
}

.type-badge.video {
  background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
  color: #ad1457;
  border: 1px solid #f48fb1;
}

.type-badge.background-image {
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
  color: #6a1b9a;
  border: 1px solid #ce93d8;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  gap: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  color: white;
  box-shadow: 0 4px 8px rgba(86, 171, 47, 0.3);
}

.btn-success:hover {
  box-shadow: 0 6px 16px rgba(86, 171, 47, 0.4);
}

.btn-outline {
  background: white;
  border: 2px solid #e9ecef;
  color: #495057;
  box-shadow: 0 2px 4px rgba(0,0,0,0.08);
}

.btn-outline:hover {
  background: #f8f9fa;
  border-color: #667eea;
  color: #667eea;
}

.btn-small {
  padding: 4px 8px;
  font-size: 10px;
  min-width: 32px;
  border-radius: 6px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon {
  padding: 8px;
  background: rgba(255,255,255,0.2);
  color: white;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon:hover {
  background: rgba(255,255,255,0.3);
  transform: scale(1.1);
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #6c757d;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

/* 底部信息 */
.footer {
  padding: 12px 16px;
  background: white;
  border-top: 1px solid #e9ecef;
}

.page-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.page-url {
  font-size: 11px;
  color: #6c757d;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 设置面板 */
.settings-panel {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  z-index: 1000;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease-in-out;
}

.settings-panel.show {
  opacity: 1;
  transform: translateX(0);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.settings-header h3 {
  margin: 0;
  color: #495057;
  font-size: 18px;
}

.settings-header .btn-icon {
  background: #dc3545;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.settings-header .btn-icon:hover {
  background: #c82333;
  transform: scale(1.1);
}

.settings-content {
  padding: 16px;
}

.setting-item {
  margin-bottom: 16px;
}

.setting-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.setting-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

.setting-item input[type="number"] {
  width: 80px;
  padding: 4px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  margin-left: 8px;
}

/* 域名分组样式 */
.domain-group {
  margin-bottom: 16px;
}

.domain-header {
  background: #f8f9fa;
  padding: 8px 12px;
  font-weight: 600;
  font-size: 13px;
  color: #495057;
  border-radius: 4px 4px 0 0;
  border-bottom: 1px solid #e9ecef;
  position: sticky;
  top: 0;
  z-index: 10;
}

.domain-group .media-item {
  border-radius: 0;
  margin-bottom: 1px;
}

.domain-group .media-item:last-child {
  border-radius: 0 0 8px 8px;
}

/* Toast 通知样式 */
.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #333;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 10000;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.toast.show {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 450px) {
  body {
    width: 350px;
  }

  .filters {
    flex-direction: column;
  }

  .batch-actions {
    flex-wrap: wrap;
  }

  .media-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .media-actions {
    align-self: flex-end;
    margin-top: 8px;
  }
}

/* 滚动条样式 */
.media-list::-webkit-scrollbar {
  width: 6px;
}

.media-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.media-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.media-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
.media-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态改进 */
.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
