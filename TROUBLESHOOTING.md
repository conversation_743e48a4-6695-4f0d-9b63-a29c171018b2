# Vision Sniffer 故障排除指南

## 🚨 媒体列表不显示问题

### 快速诊断步骤

#### 1. 重新加载插件
```
1. 访问 chrome://extensions/
2. 找到 Vision Sniffer 插件
3. 点击 "重新加载" 按钮
4. 确认插件状态为 "已启用"
```

#### 2. 打开调试页面
```
打开: file:///Users/<USER>/Downloads/我自己的代码/visionSniffer/media_list_debug.html
```

#### 3. 检查控制台日志

**页面控制台 (F12 → Console):**
```
应该看到:
✅ Vision Sniffer: 开始扫描媒体文件...
✅ Vision Sniffer: 找到 6 个img元素
✅ Vision Sniffer: 扫描完成，总共发现 10 个媒体文件
```

**插件弹窗控制台 (右键弹窗 → 检查 → Console):**
```
应该看到:
✅ PopupManager: 构造函数开始...
✅ PopupManager: 开始初始化...
✅ PopupManager: 收到 10 个媒体项目
✅ PopupManager: 过滤后的媒体项目数量: 10
✅ PopupManager: 媒体列表渲染完成
```

### 常见问题及解决方案

#### 问题1: 扫描没有找到媒体文件
**症状:** 页面控制台显示 "发现 0 个媒体文件"

**可能原因:**
- URL验证过于严格
- 图片加载失败
- 网络问题

**解决方案:**
1. 检查图片是否能正常加载
2. 尝试使用本地测试页面
3. 检查网络连接

#### 问题2: 消息传递失败
**症状:** 弹窗控制台显示 "Failed to load media items"

**可能原因:**
- Content script未正确注入
- 标签页ID错误
- 权限问题

**解决方案:**
1. 刷新页面后再打开插件
2. 检查插件权限设置
3. 重新安装插件

#### 问题3: 过滤器过滤掉所有项目
**症状:** 弹窗控制台显示 "过滤后的媒体项目数量: 0"

**可能原因:**
- 最小文件大小设置过高
- 搜索框有内容
- 类型过滤器设置错误

**解决方案:**
1. 重置所有过滤器
2. 清空搜索框
3. 检查设置面板中的最小文件大小

#### 问题4: HTML渲染失败
**症状:** 弹窗控制台显示 "生成的HTML长度: 0"

**可能原因:**
- createMediaItemHTML函数错误
- 模板字符串语法错误
- JavaScript执行错误

**解决方案:**
1. 检查浏览器控制台是否有JavaScript错误
2. 使用测试按钮进行调试
3. 检查HTML模板语法

### 调试工具

#### 1. 使用测试按钮
在插件弹窗中会出现一个红色的"测试媒体列表渲染"按钮，点击它可以：
- 创建测试数据
- 直接测试HTML生成
- 绕过消息传递机制

#### 2. 浏览器开发者工具
```javascript
// 在弹窗控制台中执行以下命令进行调试:

// 检查PopupManager实例
console.log(window.popupManagerInstance);

// 检查媒体项目
console.log(window.popupManagerInstance.mediaItems);

// 检查过滤后的项目
console.log(window.popupManagerInstance.filteredItems);

// 手动触发渲染
window.popupManagerInstance.renderMediaList();
```

#### 3. 检查DOM元素
```javascript
// 检查关键DOM元素是否存在
console.log('mediaList元素:', document.getElementById('mediaList'));
console.log('loadingIndicator元素:', document.getElementById('loadingIndicator'));
console.log('emptyState元素:', document.getElementById('emptyState'));
```

### 逐步排查流程

#### 第1步: 基础检查
- [ ] 插件已正确安装并启用
- [ ] 浏览器版本兼容 (Chrome 88+)
- [ ] 没有其他插件冲突

#### 第2步: 扫描检查
- [ ] 页面控制台有扫描日志
- [ ] 扫描找到了媒体文件
- [ ] 没有JavaScript错误

#### 第3步: 通信检查
- [ ] 弹窗能正常打开
- [ ] 弹窗控制台有通信日志
- [ ] 消息传递成功

#### 第4步: 过滤检查
- [ ] 过滤器设置正确
- [ ] 过滤后有剩余项目
- [ ] 搜索框为空

#### 第5步: 渲染检查
- [ ] HTML生成成功
- [ ] DOM元素存在
- [ ] CSS样式正确

### 紧急修复方案

如果以上方法都无效，尝试以下紧急修复：

#### 方案1: 重置插件
```
1. 完全卸载插件
2. 重启浏览器
3. 重新安装插件
4. 清除浏览器缓存
```

#### 方案2: 使用简化版本
创建一个最简版本的popup.js，只包含基本功能：
```javascript
document.addEventListener('DOMContentLoaded', async () => {
  const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
  const response = await chrome.tabs.sendMessage(tab.id, { action: 'getMediaItems' });
  
  if (response && response.mediaItems) {
    const mediaList = document.getElementById('mediaList');
    mediaList.innerHTML = response.mediaItems.map(item => 
      `<div>${item.type}: ${item.url}</div>`
    ).join('');
  }
});
```

#### 方案3: 检查文件完整性
确认以下文件存在且内容正确：
- [ ] manifest.json
- [ ] content.js
- [ ] popup.html
- [ ] popup.css
- [ ] popup.js
- [ ] background.js

### 联系支持

如果问题仍然存在，请提供以下信息：
1. Chrome版本号
2. 操作系统版本
3. 完整的控制台日志
4. 插件安装步骤
5. 测试页面URL
6. 具体的错误现象

---

**记住:** 每次修改代码后都要重新加载插件！
