<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="64" cy="64" r="60" fill="url(#grad1)" stroke="#fff" stroke-width="4"/>
  
  <!-- 相机镜头 -->
  <circle cx="64" cy="64" r="35" fill="none" stroke="#fff" stroke-width="3"/>
  <circle cx="64" cy="64" r="25" fill="none" stroke="#fff" stroke-width="2"/>
  <circle cx="64" cy="64" r="15" fill="#fff" opacity="0.8"/>
  
  <!-- 视频播放图标 -->
  <polygon points="52,45 52,83 85,64" fill="#fff" opacity="0.6"/>
  
  <!-- 搜索放大镜 -->
  <circle cx="90" cy="38" r="12" fill="none" stroke="#fff" stroke-width="3"/>
  <line x1="99" y1="47" x2="108" y2="56" stroke="#fff" stroke-width="3" stroke-linecap="round"/>
  
  <!-- 装饰性光点 -->
  <circle cx="45" cy="35" r="3" fill="#fff" opacity="0.7"/>
  <circle cx="85" cy="95" r="2" fill="#fff" opacity="0.5"/>
  <circle cx="35" cy="85" r="2" fill="#fff" opacity="0.6"/>
</svg>
