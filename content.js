// Vision Sniffer Content Script
// 用于识别网页中的所有图片和视频

class MediaSniffer {
  constructor() {
    this.mediaItems = [];
    this.observer = null;
    this.isScanning = false;
  }

  // 初始化媒体嗅探器
  init() {
    this.scanExistingMedia();
    this.setupMutationObserver();
    this.setupMessageListener();
  }

  // 扫描现有的媒体元素
  scanExistingMedia() {
    this.isScanning = true;
    this.mediaItems = [];

    console.log('Vision Sniffer: 开始扫描媒体文件...');

    // 扫描图片
    this.scanImages();
    console.log(`Vision Sniffer: 扫描图片完成，发现 ${this.mediaItems.filter(item => item.type === 'image').length} 个图片`);

    // 扫描视频
    this.scanVideos();
    console.log(`Vision Sniffer: 扫描视频完成，发现 ${this.mediaItems.filter(item => item.type === 'video').length} 个视频`);

    // 扫描背景图片
    this.scanBackgroundImages();
    console.log(`Vision Sniffer: 扫描背景图片完成，发现 ${this.mediaItems.filter(item => item.type === 'background-image').length} 个背景图片`);

    // 扫描iframe中的媒体（如果可访问）
    this.scanIframes();

    this.isScanning = false;
    console.log(`Vision Sniffer: 扫描完成，总共发现 ${this.mediaItems.length} 个媒体文件`);
    console.log('Vision Sniffer: 媒体文件列表:', this.mediaItems);
  }

  // 扫描图片元素
  scanImages() {
    const images = document.querySelectorAll('img');
    console.log(`Vision Sniffer: 找到 ${images.length} 个img元素`);

    images.forEach((img, index) => {
      console.log(`Vision Sniffer: 检查图片 ${index + 1}: ${img.src}`);

      if (img.src && this.isValidUrl(img.src)) {
        console.log(`Vision Sniffer: 添加有效图片: ${img.src}`);
        this.addMediaItem({
          type: 'image',
          url: img.src,
          alt: img.alt || '',
          width: img.naturalWidth || img.width,
          height: img.naturalHeight || img.height,
          element: img,
          size: this.estimateImageSize(img)
        });
      } else {
        console.log(`Vision Sniffer: 跳过无效图片: ${img.src} (isValid: ${this.isValidUrl(img.src)})`);
      }
    });
  }

  // 扫描视频元素
  scanVideos() {
    const videos = document.querySelectorAll('video');
    videos.forEach(video => {
      if (video.src && this.isValidUrl(video.src)) {
        this.addMediaItem({
          type: 'video',
          url: video.src,
          width: video.videoWidth || video.width,
          height: video.videoHeight || video.height,
          duration: video.duration,
          element: video,
          poster: video.poster
        });
      }

      // 检查video标签内的source元素
      const sources = video.querySelectorAll('source');
      sources.forEach(source => {
        if (source.src && this.isValidUrl(source.src)) {
          this.addMediaItem({
            type: 'video',
            url: source.src,
            width: video.videoWidth || video.width,
            height: video.videoHeight || video.height,
            duration: video.duration,
            element: video,
            poster: video.poster,
            format: source.type
          });
        }
      });
    });
  }

  // 扫描CSS背景图片
  scanBackgroundImages() {
    const elements = document.querySelectorAll('*');
    elements.forEach(element => {
      const style = window.getComputedStyle(element);
      const backgroundImage = style.backgroundImage;

      if (backgroundImage && backgroundImage !== 'none') {
        const urlMatch = backgroundImage.match(/url\(['"]?(.*?)['"]?\)/);
        if (urlMatch && urlMatch[1] && this.isValidUrl(urlMatch[1])) {
          this.addMediaItem({
            type: 'background-image',
            url: urlMatch[1],
            element: element,
            width: element.offsetWidth,
            height: element.offsetHeight
          });
        }
      }
    });
  }

  // 检查URL是否有效
  isValidUrl(url) {
    if (!url) return false;

    // 排除data URLs, blob URLs等
    if (url.startsWith('data:') || url.startsWith('blob:')) return false;

    // 接受http, https和相对路径
    return url.startsWith('http://') ||
           url.startsWith('https://') ||
           url.startsWith('//') ||
           (url.startsWith('/') && !url.startsWith('//')) ||
           (!url.includes('://') && !url.startsWith('/'));
  }

  // 扫描iframe中的媒体
  scanIframes() {
    const iframes = document.querySelectorAll('iframe');
    iframes.forEach(iframe => {
      try {
        // 只能访问同源的iframe
        if (iframe.contentDocument) {
          const iframeImages = iframe.contentDocument.querySelectorAll('img');
          const iframeVideos = iframe.contentDocument.querySelectorAll('video');
          
          iframeImages.forEach(img => {
            if (img.src && this.isValidUrl(img.src)) {
              this.addMediaItem({
                type: 'image',
                url: img.src,
                alt: img.alt || '',
                width: img.naturalWidth || img.width,
                height: img.naturalHeight || img.height,
                element: img,
                source: 'iframe'
              });
            }
          });

          iframeVideos.forEach(video => {
            if (video.src && this.isValidUrl(video.src)) {
              this.addMediaItem({
                type: 'video',
                url: video.src,
                width: video.videoWidth || video.width,
                height: video.videoHeight || video.height,
                duration: video.duration,
                element: video,
                source: 'iframe'
              });
            }
          });
        }
      } catch (e) {
        // 跨域iframe无法访问，忽略错误
      }
    });
  }

  // 添加媒体项目
  addMediaItem(item) {
    // 避免重复添加
    const exists = this.mediaItems.some(existing => existing.url === item.url);
    if (!exists) {
      item.id = this.generateId();
      item.timestamp = Date.now();
      item.domain = new URL(item.url).hostname;
      this.mediaItems.push(item);
    }
  }

  // 估算图片大小
  estimateImageSize(img) {
    const width = img.naturalWidth || img.width;
    const height = img.naturalHeight || img.height;
    // 粗略估算，实际大小需要通过网络请求获取
    return width && height ? Math.round(width * height * 0.3) : 0;
  }

  // 生成唯一ID
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  // 设置DOM变化监听器
  setupMutationObserver() {
    this.observer = new MutationObserver((mutations) => {
      let shouldRescan = false;
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              if (node.tagName === 'IMG' || node.tagName === 'VIDEO' || 
                  node.querySelector('img, video')) {
                shouldRescan = true;
              }
            }
          });
        }
      });
      
      if (shouldRescan && !this.isScanning) {
        setTimeout(() => this.scanExistingMedia(), 1000);
      }
    });
    
    this.observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  // 设置消息监听器
  setupMessageListener() {
    chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
      console.log('Content script收到消息:', request);

      if (request.action === 'ping') {
        console.log('Content script响应ping');
        sendResponse({ pong: true, mediaCount: this.mediaItems.length });
      } else if (request.action === 'getMediaItems') {
        console.log('Content script响应getMediaItems，返回', this.mediaItems.length, '个项目');
        sendResponse({
          mediaItems: this.mediaItems,
          url: window.location.href,
          title: document.title
        });
      } else if (request.action === 'rescanMedia') {
        console.log('Content script响应rescanMedia');
        // 重新扫描后再发送响应
        this.scanExistingMedia();
        // 使用setTimeout确保扫描完成后再响应
        setTimeout(() => {
          console.log('Content script rescan完成，返回', this.mediaItems.length, '个项目');
          sendResponse({
            mediaItems: this.mediaItems,
            url: window.location.href,
            title: document.title
          });
        }, 100);
        return true; // 保持消息通道开放
      } else if (request.action === 'highlightElement') {
        console.log('Content script响应highlightElement');
        this.highlightElement(request.id);
        sendResponse({ success: true });
      }
      return true;
    });
  }

  // 高亮显示媒体元素
  highlightElement(id) {
    const item = this.mediaItems.find(item => item.id === id);
    if (item && item.element) {
      item.element.style.outline = '3px solid #ff4444';
      item.element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      
      setTimeout(() => {
        item.element.style.outline = '';
      }, 3000);
    }
  }
}

// 初始化媒体嗅探器
const mediaSniffer = new MediaSniffer();

// 页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => mediaSniffer.init());
} else {
  mediaSniffer.init();
}

// 导出到全局作用域供调试使用
window.mediaSniffer = mediaSniffer;
