// Vision Sniffer Popup Script
// 管理弹出窗口的用户界面和交互

console.log('=== popup.js 开始加载 ===');
console.log('Chrome API 可用性:', typeof chrome !== 'undefined');
console.log('Chrome tabs API:', typeof chrome?.tabs !== 'undefined');
console.log('Chrome runtime API:', typeof chrome?.runtime !== 'undefined');

class PopupManager {
  constructor() {
    console.log('PopupManager: 构造函数开始...');
    this.mediaItems = [];
    this.filteredItems = [];
    this.selectedItems = new Set();
    this.currentTab = null;
    this.settings = {
      autoScanEnabled: true,
      showThumbnailsEnabled: true,
      groupByDomainEnabled: false,
      minSizeFilter: 0
    };

    console.log('PopupManager: 构造函数完成，开始初始化...');
    this.init();
  }

  // 初始化弹出窗口
  async init() {
    console.log('PopupManager: 开始初始化...');
    await this.loadSettings();
    this.setupEventListeners();
    await this.getCurrentTab();
    await this.loadMediaItems();
    console.log('PopupManager: 初始化完成');
  }

  // 加载设置
  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get(this.settings);
      this.settings = { ...this.settings, ...result };
      this.applySettings();
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }

  // 应用设置
  applySettings() {
    document.getElementById('autoScanEnabled').checked = this.settings.autoScanEnabled;
    document.getElementById('showThumbnailsEnabled').checked = this.settings.showThumbnailsEnabled;
    document.getElementById('groupByDomainEnabled').checked = this.settings.groupByDomainEnabled;
    document.getElementById('minSizeFilter').value = this.settings.minSizeFilter;
  }

  // 保存设置
  async saveSettings() {
    try {
      await chrome.storage.sync.set(this.settings);
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  }

  // 设置事件监听器
  setupEventListeners() {
    // 刷新按钮
    document.getElementById('refreshBtn').addEventListener('click', () => {
      this.refreshMediaItems();
    });

    // 设置按钮
    document.getElementById('settingsBtn').addEventListener('click', () => {
      this.toggleSettingsPanel();
    });

    // 关闭设置按钮
    document.getElementById('closeSettingsBtn').addEventListener('click', () => {
      this.toggleSettingsPanel();
    });

    // 返回主界面按钮
    document.getElementById('backToMainBtn').addEventListener('click', () => {
      this.toggleSettingsPanel();
    });

    // 键盘快捷键支持
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        const panel = document.getElementById('settingsPanel');
        if (panel.classList.contains('show')) {
          this.toggleSettingsPanel();
        }
      }
    });

    // 过滤器
    document.getElementById('typeFilter').addEventListener('change', () => {
      this.applyFilters();
      this.renderMediaList();
    });

    document.getElementById('sizeFilter').addEventListener('change', () => {
      this.applyFilters();
      this.renderMediaList();
    });

    document.getElementById('searchInput').addEventListener('input', () => {
      this.applyFilters();
      this.renderMediaList();
    });

    // 批量操作
    document.getElementById('selectAllBtn').addEventListener('click', () => {
      this.selectAll();
    });

    document.getElementById('selectNoneBtn').addEventListener('click', () => {
      this.selectNone();
    });

    document.getElementById('downloadSelectedBtn').addEventListener('click', () => {
      this.downloadSelected();
    });

    // 重新扫描按钮
    document.getElementById('rescanBtn').addEventListener('click', () => {
      this.refreshMediaItems();
    });

    // 设置项
    document.getElementById('autoScanEnabled').addEventListener('change', (e) => {
      this.settings.autoScanEnabled = e.target.checked;
      this.saveSettings();
    });

    document.getElementById('showThumbnailsEnabled').addEventListener('change', (e) => {
      this.settings.showThumbnailsEnabled = e.target.checked;
      this.saveSettings();
      this.renderMediaList();
    });

    document.getElementById('groupByDomainEnabled').addEventListener('change', (e) => {
      this.settings.groupByDomainEnabled = e.target.checked;
      this.saveSettings();
      this.renderMediaList();
    });

    document.getElementById('minSizeFilter').addEventListener('change', (e) => {
      this.settings.minSizeFilter = parseInt(e.target.value) || 0;
      this.saveSettings();
      this.applyFilters();
      this.renderMediaList();
    });
  }

  // 获取当前标签页
  async getCurrentTab() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      this.currentTab = tab;
      this.updatePageInfo();
    } catch (error) {
      console.error('Failed to get current tab:', error);
    }
  }

  // 更新页面信息
  updatePageInfo() {
    if (this.currentTab) {
      document.getElementById('pageTitle').textContent = this.currentTab.title || '未知页面';
      document.getElementById('pageUrl').textContent = this.currentTab.url || '';
    }
  }

  // 加载媒体项目
  async loadMediaItems() {
    console.log('PopupManager: 开始加载媒体项目...');
    this.showLoading();

    try {
      if (!this.currentTab) {
        console.log('PopupManager: 获取当前标签页...');
        await this.getCurrentTab();
      }

      console.log('PopupManager: 发送getMediaItems消息到标签页:', this.currentTab.id);
      const response = await chrome.tabs.sendMessage(this.currentTab.id, {
        action: 'getMediaItems'
      });

      console.log('PopupManager: 收到响应:', response);

      if (response && response.mediaItems) {
        console.log(`PopupManager: 收到 ${response.mediaItems.length} 个媒体项目`);
        this.mediaItems = response.mediaItems;
        this.applyFilters();
        this.updateStats();
        this.renderMediaList();
      } else {
        console.log('PopupManager: 没有收到有效的媒体项目，显示空状态');
        this.showEmptyState();
      }
    } catch (error) {
      console.error('PopupManager: 加载媒体项目失败:', error);
      this.showEmptyState();
    }
  }

  // 刷新媒体项目
  async refreshMediaItems() {
    console.log('PopupManager: 开始刷新媒体项目...');
    this.showLoading();

    try {
      console.log('PopupManager: 发送rescanMedia消息到标签页:', this.currentTab.id);
      const response = await chrome.tabs.sendMessage(this.currentTab.id, {
        action: 'rescanMedia'
      });

      console.log('PopupManager: 收到响应:', response);

      if (response && response.mediaItems) {
        console.log(`PopupManager: 收到 ${response.mediaItems.length} 个媒体项目`);
        this.mediaItems = response.mediaItems;
        this.selectedItems.clear();
        this.applyFilters();
        this.updateStats();
        this.renderMediaList();
      } else {
        console.log('PopupManager: 没有收到有效的媒体项目，显示空状态');
        this.showEmptyState();
      }
    } catch (error) {
      console.error('PopupManager: 刷新媒体项目失败:', error);
      this.showEmptyState();
    }
  }

  // 应用过滤器
  applyFilters() {
    console.log('PopupManager: 开始应用过滤器...');
    console.log('PopupManager: 原始媒体项目数量:', this.mediaItems.length);

    const typeFilter = document.getElementById('typeFilter')?.value || 'all';
    const sizeFilter = document.getElementById('sizeFilter')?.value || 'all';
    const searchQuery = document.getElementById('searchInput')?.value?.toLowerCase() || '';

    console.log('PopupManager: 过滤条件 - 类型:', typeFilter, '大小:', sizeFilter, '搜索:', searchQuery);

    this.filteredItems = this.mediaItems.filter(item => {
      // 类型过滤
      if (typeFilter !== 'all' && item.type !== typeFilter) {
        return false;
      }

      // 大小过滤
      if (sizeFilter !== 'all') {
        const size = item.size || 0;
        switch (sizeFilter) {
          case 'small':
            if (size >= 100 * 1024) return false;
            break;
          case 'medium':
            if (size < 100 * 1024 || size >= 1024 * 1024) return false;
            break;
          case 'large':
            if (size < 1024 * 1024) return false;
            break;
        }
      }

      // 最小大小过滤
      if (this.settings.minSizeFilter > 0) {
        const size = item.size || 0;
        if (size < this.settings.minSizeFilter * 1024) return false;
      }

      // 搜索过滤
      if (searchQuery) {
        const searchText = `${item.url} ${item.domain} ${item.alt || ''}`.toLowerCase();
        if (!searchText.includes(searchQuery)) return false;
      }

      return true;
    });

    console.log('PopupManager: 过滤后的媒体项目数量:', this.filteredItems.length);
    console.log('PopupManager: 过滤后的媒体项目:', this.filteredItems);
  }

  // 更新统计信息
  updateStats() {
    const imageCount = this.mediaItems.filter(item => item.type === 'image').length;
    const videoCount = this.mediaItems.filter(item => item.type === 'video').length;
    const totalCount = this.mediaItems.length;

    document.getElementById('imageCount').textContent = imageCount;
    document.getElementById('videoCount').textContent = videoCount;
    document.getElementById('totalCount').textContent = totalCount;
    
    this.updateSelectedCount();
  }

  // 更新选中数量
  updateSelectedCount() {
    document.getElementById('selectedCount').textContent = `已选择: ${this.selectedItems.size}`;
  }

  // 显示加载状态
  showLoading() {
    document.getElementById('loadingIndicator').style.display = 'flex';
    document.getElementById('emptyState').style.display = 'none';
    document.getElementById('mediaList').innerHTML = '';
  }

  // 显示空状态
  showEmptyState() {
    document.getElementById('loadingIndicator').style.display = 'none';
    document.getElementById('emptyState').style.display = 'block';
  }

  // 隐藏加载和空状态
  hideLoadingAndEmpty() {
    document.getElementById('loadingIndicator').style.display = 'none';
    document.getElementById('emptyState').style.display = 'none';
  }

  // 渲染媒体列表
  renderMediaList() {
    console.log('PopupManager: 开始渲染媒体列表...');
    console.log('PopupManager: 过滤后的项目数量:', this.filteredItems.length);

    this.hideLoadingAndEmpty();

    const mediaList = document.getElementById('mediaList');
    console.log('PopupManager: mediaList元素:', mediaList);

    if (this.filteredItems.length === 0) {
      console.log('PopupManager: 没有过滤后的项目，显示空状态');
      this.showEmptyState();
      return;
    }

    let html = '';

    if (this.settings.groupByDomainEnabled) {
      console.log('PopupManager: 使用域名分组渲染');
      html = this.renderGroupedByDomain();
    } else {
      console.log('PopupManager: 使用平铺列表渲染');
      html = this.renderFlatList();
    }

    console.log('PopupManager: 生成的HTML长度:', html.length);
    console.log('PopupManager: 生成的HTML预览:', html.substring(0, 200) + '...');

    mediaList.innerHTML = html;
    this.attachMediaItemListeners();

    console.log('PopupManager: 媒体列表渲染完成');
  }

  // 渲染平铺列表
  renderFlatList() {
    return this.filteredItems.map(item => this.createMediaItemHTML(item)).join('');
  }

  // 按域名分组渲染
  renderGroupedByDomain() {
    const groups = {};

    this.filteredItems.forEach(item => {
      const domain = item.domain || 'unknown';
      if (!groups[domain]) {
        groups[domain] = [];
      }
      groups[domain].push(item);
    });

    let html = '';
    Object.keys(groups).sort().forEach(domain => {
      html += `<div class="domain-group">
        <div class="domain-header">${domain} (${groups[domain].length})</div>
        ${groups[domain].map(item => this.createMediaItemHTML(item)).join('')}
      </div>`;
    });

    return html;
  }

  // 创建媒体项目HTML
  createMediaItemHTML(item) {
    const isSelected = this.selectedItems.has(item.id);
    const thumbnailSrc = this.settings.showThumbnailsEnabled && item.type === 'image' ? item.url : '';
    const size = this.formatFileSize(item.size);
    const dimensions = item.width && item.height ? `${item.width}×${item.height}` : '';

    return `
      <div class="media-item ${isSelected ? 'selected' : ''}" data-id="${item.id}">
        <input type="checkbox" class="media-checkbox" ${isSelected ? 'checked' : ''}>
        <img class="media-thumbnail" src="${thumbnailSrc}" alt="缩略图"
             onerror="this.style.display='none'"
             style="${thumbnailSrc ? '' : 'display:none'}">
        <div class="media-info">
          <div class="media-title">
            ${this.getMediaTitle(item)}
            <span class="type-badge ${item.type}">${this.getTypeLabel(item.type)}</span>
          </div>
          <div class="media-details">
            ${size ? `<span>${size}</span>` : ''}
            ${dimensions ? `<span>${dimensions}</span>` : ''}
            <span>${item.domain}</span>
          </div>
          <div class="media-url" title="${item.url}">${item.url}</div>
        </div>
        <div class="media-actions">
          <button class="btn btn-small btn-outline download-btn" title="下载">⬇️</button>
          <button class="btn btn-small btn-outline highlight-btn" title="高亮">🎯</button>
          <button class="btn btn-small btn-outline copy-btn" title="复制">📋</button>
        </div>
      </div>
    `;
  }

  // 获取媒体标题
  getMediaTitle(item) {
    if (item.alt) return item.alt;

    try {
      const url = new URL(item.url);
      const filename = url.pathname.split('/').pop();
      return filename || 'Untitled';
    } catch {
      return 'Untitled';
    }
  }

  // 获取类型标签
  getTypeLabel(type) {
    const labels = {
      'image': '图片',
      'video': '视频',
      'background-image': '背景图'
    };
    return labels[type] || type;
  }

  // 格式化文件大小
  formatFileSize(bytes) {
    if (!bytes) return '';

    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  // 附加媒体项目事件监听器
  attachMediaItemListeners() {
    // 复选框事件
    document.querySelectorAll('.media-checkbox').forEach(checkbox => {
      checkbox.addEventListener('change', (e) => {
        const mediaItem = e.target.closest('.media-item');
        const itemId = mediaItem.dataset.id;

        if (e.target.checked) {
          this.selectedItems.add(itemId);
          mediaItem.classList.add('selected');
        } else {
          this.selectedItems.delete(itemId);
          mediaItem.classList.remove('selected');
        }

        this.updateSelectedCount();
      });
    });

    // 下载按钮事件
    document.querySelectorAll('.download-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const mediaItem = e.target.closest('.media-item');
        const itemId = mediaItem.dataset.id;
        this.downloadMediaItem(itemId);
      });
    });

    // 高亮按钮事件
    document.querySelectorAll('.highlight-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const mediaItem = e.target.closest('.media-item');
        const itemId = mediaItem.dataset.id;
        this.highlightMediaItem(itemId);
      });
    });

    // 复制按钮事件
    document.querySelectorAll('.copy-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const mediaItem = e.target.closest('.media-item');
        const itemId = mediaItem.dataset.id;
        this.copyMediaUrl(itemId);
      });
    });
  }

  // 全选
  selectAll() {
    this.filteredItems.forEach(item => {
      this.selectedItems.add(item.id);
    });
    this.renderMediaList();
  }

  // 取消全选
  selectNone() {
    this.selectedItems.clear();
    this.renderMediaList();
  }

  // 下载选中的项目
  async downloadSelected() {
    const selectedMediaItems = this.filteredItems.filter(item =>
      this.selectedItems.has(item.id)
    );

    if (selectedMediaItems.length === 0) {
      alert('请先选择要下载的媒体文件');
      return;
    }

    try {
      const downloadItems = selectedMediaItems.map(item => ({
        url: item.url,
        filename: this.generateFilename(item)
      }));

      const response = await chrome.runtime.sendMessage({
        action: 'downloadBatch',
        items: downloadItems
      });

      if (response.success) {
        alert(`开始下载 ${downloadItems.length} 个文件`);
      } else {
        alert(`下载失败: ${response.error}`);
      }
    } catch (error) {
      console.error('Download failed:', error);
      alert('下载失败，请重试');
    }
  }

  // 下载单个媒体项目
  async downloadMediaItem(itemId) {
    const item = this.mediaItems.find(item => item.id === itemId);
    if (!item) return;

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'downloadMedia',
        url: item.url,
        filename: this.generateFilename(item)
      });

      if (response.success) {
        console.log('Download started:', item.url);
      } else {
        alert(`下载失败: ${response.error}`);
      }
    } catch (error) {
      console.error('Download failed:', error);
      alert('下载失败，请重试');
    }
  }

  // 高亮媒体项目
  async highlightMediaItem(itemId) {
    try {
      await chrome.tabs.sendMessage(this.currentTab.id, {
        action: 'highlightElement',
        id: itemId
      });
    } catch (error) {
      console.error('Highlight failed:', error);
    }
  }

  // 复制媒体URL
  async copyMediaUrl(itemId) {
    const item = this.mediaItems.find(item => item.id === itemId);
    if (!item) return;

    try {
      await navigator.clipboard.writeText(item.url);
      // 显示复制成功的提示
      this.showToast('链接已复制到剪贴板');
    } catch (error) {
      console.error('Copy failed:', error);
      // 降级方案
      try {
        const textArea = document.createElement('textarea');
        textArea.value = item.url;
        textArea.style.position = 'fixed';
        textArea.style.opacity = '0';
        document.body.appendChild(textArea);
        textArea.select();
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
          this.showToast('链接已复制到剪贴板');
        } else {
          this.showToast('复制失败，请手动复制');
        }
      } catch (fallbackError) {
        console.error('Fallback copy failed:', fallbackError);
        this.showToast('复制失败，请手动复制');
      }
    }
  }

  // 生成文件名
  generateFilename(item) {
    try {
      const url = new URL(item.url);
      const pathname = url.pathname;
      const filename = pathname.split('/').pop();

      if (filename && filename.includes('.')) {
        return filename;
      } else {
        const extension = this.guessExtension(item);
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        return `media_${timestamp}${extension}`;
      }
    } catch (error) {
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      return `media_${timestamp}`;
    }
  }

  // 猜测文件扩展名
  guessExtension(item) {
    const url = item.url.toLowerCase();

    if (item.type === 'video') {
      if (url.includes('.mp4')) return '.mp4';
      if (url.includes('.webm')) return '.webm';
      if (url.includes('.ogg')) return '.ogg';
      return '.mp4';
    } else {
      if (url.includes('.jpg') || url.includes('.jpeg')) return '.jpg';
      if (url.includes('.png')) return '.png';
      if (url.includes('.gif')) return '.gif';
      if (url.includes('.webp')) return '.webp';
      if (url.includes('.svg')) return '.svg';
      return '.jpg';
    }
  }

  // 切换设置面板
  toggleSettingsPanel() {
    const panel = document.getElementById('settingsPanel');
    const isVisible = panel.classList.contains('show');

    console.log('PopupManager: 切换设置面板，当前状态:', isVisible ? '显示' : '隐藏');

    if (isVisible) {
      // 隐藏设置面板
      panel.classList.remove('show');
      // 延迟隐藏，等待动画完成
      setTimeout(() => {
        panel.style.display = 'none';
      }, 300);
      console.log('PopupManager: 隐藏设置面板');
    } else {
      // 显示设置面板
      panel.style.display = 'block';
      // 强制重绘后添加show类以触发动画
      requestAnimationFrame(() => {
        panel.classList.add('show');
      });
      console.log('PopupManager: 显示设置面板');
    }
  }

  // 显示提示消息
  showToast(message) {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #333;
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      font-size: 12px;
      z-index: 10000;
      opacity: 0;
      transition: opacity 0.3s ease;
    `;

    document.body.appendChild(toast);

    // 显示动画
    setTimeout(() => {
      toast.style.opacity = '1';
    }, 100);

    // 自动隐藏
    setTimeout(() => {
      toast.style.opacity = '0';
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    }, 2000);
  }
}

// 测试函数 - 用于调试
function testMediaListRendering() {
  console.log('=== 开始媒体列表渲染测试 ===');

  // 创建测试数据
  const testItems = [
    {
      id: 'test1',
      type: 'image',
      url: 'https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=Test+1',
      alt: '测试图片1',
      width: 300,
      height: 200,
      domain: 'via.placeholder.com',
      size: 15000
    },
    {
      id: 'test2',
      type: 'image',
      url: 'https://via.placeholder.com/300x200/4ECDC4/FFFFFF?text=Test+2',
      alt: '测试图片2',
      width: 300,
      height: 200,
      domain: 'via.placeholder.com',
      size: 16000
    }
  ];

  // 直接测试HTML生成
  const popupManager = window.popupManagerInstance;
  if (popupManager) {
    console.log('找到PopupManager实例，开始测试...');
    popupManager.mediaItems = testItems;
    popupManager.filteredItems = testItems;

    const html = popupManager.renderFlatList();
    console.log('生成的HTML:', html);

    const mediaList = document.getElementById('mediaList');
    if (mediaList) {
      mediaList.innerHTML = html;
      console.log('HTML已插入到mediaList元素');
    } else {
      console.error('找不到mediaList元素');
    }
  } else {
    console.error('找不到PopupManager实例');
  }

  console.log('=== 媒体列表渲染测试完成 ===');
}

// 简化的直接测试函数
async function directTest() {
  console.log('=== 直接测试开始 ===');

  try {
    // 检查Chrome API
    console.log('检查Chrome API...');
    if (typeof chrome === 'undefined') {
      console.error('Chrome API不可用');
      return;
    }

    if (typeof chrome.tabs === 'undefined') {
      console.error('Chrome tabs API不可用');
      return;
    }

    console.log('Chrome API检查通过');

    // 获取当前标签页
    console.log('获取当前标签页...');
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    console.log('查询到的标签页:', tabs);

    if (!tabs || tabs.length === 0) {
      console.error('没有找到活动标签页');
      return;
    }

    const tab = tabs[0];
    console.log('当前标签页ID:', tab.id, '标签页URL:', tab.url);

    // 直接发送消息
    console.log('发送消息到content script...');
    const response = await chrome.tabs.sendMessage(tab.id, { action: 'getMediaItems' });
    console.log('收到响应:', response);

    if (response && response.mediaItems) {
      console.log(`收到 ${response.mediaItems.length} 个媒体项目`);
      console.log('媒体项目示例:', response.mediaItems.slice(0, 3));

      // 直接渲染到mediaList
      const mediaList = document.getElementById('mediaList');
      console.log('mediaList元素:', mediaList);

      if (mediaList) {
        // 隐藏加载状态
        const loadingIndicator = document.getElementById('loadingIndicator');
        const emptyState = document.getElementById('emptyState');

        if (loadingIndicator) loadingIndicator.style.display = 'none';
        if (emptyState) emptyState.style.display = 'none';

        // 生成简单的HTML
        const html = response.mediaItems.map((item, index) => `
          <div style="border: 1px solid #ddd; margin: 5px; padding: 10px; border-radius: 4px; background: #f8f9fa;">
            <div style="font-weight: bold; color: #007bff;">${index + 1}. ${item.type.toUpperCase()}</div>
            <div style="font-size: 12px; color: #666; margin: 5px 0;">
              <strong>URL:</strong> ${item.url.length > 60 ? item.url.substring(0, 60) + '...' : item.url}
            </div>
            <div style="font-size: 11px; color: #999;">
              域名: ${item.domain || 'N/A'} | 尺寸: ${item.width || '?'}x${item.height || '?'}
            </div>
          </div>
        `).join('');

        console.log('生成HTML长度:', html.length);
        console.log('HTML预览:', html.substring(0, 200) + '...');

        mediaList.innerHTML = html;
        console.log('HTML已插入到mediaList');

        // 验证插入是否成功
        console.log('mediaList当前内容长度:', mediaList.innerHTML.length);
        console.log('mediaList子元素数量:', mediaList.children.length);

      } else {
        console.error('找不到mediaList元素');
      }
    } else {
      console.log('没有收到有效的媒体项目，响应内容:', response);
    }
  } catch (error) {
    console.error('直接测试失败:', error);
    console.error('错误详情:', error.message);
    console.error('错误堆栈:', error.stack);
  }

  console.log('=== 直接测试完成 ===');
}

// 最简单的测试函数
function simpleTest() {
  console.log('=== 简单测试开始 ===');

  const mediaList = document.getElementById('mediaList');
  console.log('mediaList元素:', mediaList);

  if (mediaList) {
    mediaList.innerHTML = '<div style="padding: 20px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; color: #155724;">测试成功！这是一个测试消息。</div>';
    console.log('测试HTML已插入');
  } else {
    console.error('找不到mediaList元素');
  }

  console.log('=== 简单测试完成 ===');
}

// 初始化弹出窗口管理器
document.addEventListener('DOMContentLoaded', () => {
  console.log('=== DOM加载完成 ===');

  // 立即添加测试按钮，不使用setTimeout
  console.log('添加测试按钮...');

  const simpleTestBtn = document.createElement('button');
  simpleTestBtn.textContent = '简单测试';
  simpleTestBtn.style.cssText = 'position: fixed; top: 10px; right: 10px; z-index: 9999; background: #007bff; color: white; border: none; padding: 8px 12px; border-radius: 4px; font-size: 12px; cursor: pointer;';
  simpleTestBtn.onclick = function() {
    console.log('简单测试按钮被点击');
    simpleTest();
  };
  document.body.appendChild(simpleTestBtn);
  console.log('简单测试按钮已添加');

  // 添加直接测试按钮
  const directTestBtn = document.createElement('button');
  directTestBtn.textContent = '直接测试';
  directTestBtn.style.cssText = 'position: fixed; top: 40px; right: 10px; z-index: 9999; background: #28a745; color: white; border: none; padding: 8px 12px; border-radius: 4px; font-size: 12px; cursor: pointer;';
  directTestBtn.onclick = function() {
    console.log('直接测试按钮被点击');
    directTest();
  };
  document.body.appendChild(directTestBtn);
  console.log('直接测试按钮已添加');

  // 尝试初始化PopupManager
  try {
    console.log('开始初始化PopupManager...');
    const popupManager = new PopupManager();
    window.popupManagerInstance = popupManager;
    console.log('PopupManager初始化成功');
  } catch (error) {
    console.error('PopupManager初始化失败:', error);
  }
});
