// Vision Sniffer Popup Script
// 管理弹出窗口的用户界面和交互

console.log('=== Vision Sniffer Popup 加载完成 ===');

class PopupManager {
  constructor() {
    this.mediaItems = [];
    this.filteredItems = [];
    this.selectedItems = new Set();
    this.currentTab = null;
    this.settings = {
      autoScanEnabled: true,
      showThumbnailsEnabled: true,
      groupByDomainEnabled: false,
      minSizeFilter: 0
    };

    this.init();
  }

  // 初始化弹出窗口
  async init() {
    await this.loadSettings();
    this.setupEventListeners();
    await this.getCurrentTab();
    await this.loadMediaItems();
  }

  // 加载设置
  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get(this.settings);
      this.settings = { ...this.settings, ...result };
      this.applySettings();
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }

  // 应用设置
  applySettings() {
    document.getElementById('autoScanEnabled').checked = this.settings.autoScanEnabled;
    document.getElementById('showThumbnailsEnabled').checked = this.settings.showThumbnailsEnabled;
    document.getElementById('groupByDomainEnabled').checked = this.settings.groupByDomainEnabled;
    document.getElementById('minSizeFilter').value = this.settings.minSizeFilter;
  }

  // 保存设置
  async saveSettings() {
    try {
      await chrome.storage.sync.set(this.settings);
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  }

  // 设置事件监听器
  setupEventListeners() {
    // 刷新按钮
    document.getElementById('refreshBtn').addEventListener('click', () => {
      this.refreshMediaItems();
    });

    // 设置按钮
    document.getElementById('settingsBtn').addEventListener('click', () => {
      this.toggleSettingsPanel();
    });

    // 关闭设置按钮
    document.getElementById('closeSettingsBtn').addEventListener('click', () => {
      this.toggleSettingsPanel();
    });

    // 返回主界面按钮
    document.getElementById('backToMainBtn').addEventListener('click', () => {
      this.toggleSettingsPanel();
    });

    // 键盘快捷键支持
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        const panel = document.getElementById('settingsPanel');
        if (panel.classList.contains('show')) {
          this.toggleSettingsPanel();
        }
      }
    });

    // 过滤器
    document.getElementById('typeFilter').addEventListener('change', () => {
      this.applyFilters();
      this.renderMediaList();
    });

    document.getElementById('sizeFilter').addEventListener('change', () => {
      this.applyFilters();
      this.renderMediaList();
    });

    document.getElementById('searchInput').addEventListener('input', () => {
      this.applyFilters();
      this.renderMediaList();
    });

    // 批量操作
    document.getElementById('selectAllBtn').addEventListener('click', () => {
      this.selectAll();
    });

    document.getElementById('selectNoneBtn').addEventListener('click', () => {
      this.selectNone();
    });

    document.getElementById('downloadSelectedBtn').addEventListener('click', () => {
      this.downloadSelected();
    });

    // 重新扫描按钮
    document.getElementById('rescanBtn').addEventListener('click', () => {
      this.refreshMediaItems();
    });

    // 设置项
    document.getElementById('autoScanEnabled').addEventListener('change', (e) => {
      this.settings.autoScanEnabled = e.target.checked;
      this.saveSettings();
    });

    document.getElementById('showThumbnailsEnabled').addEventListener('change', (e) => {
      this.settings.showThumbnailsEnabled = e.target.checked;
      this.saveSettings();
      this.renderMediaList();
    });

    document.getElementById('groupByDomainEnabled').addEventListener('change', (e) => {
      this.settings.groupByDomainEnabled = e.target.checked;
      this.saveSettings();
      this.renderMediaList();
    });

    document.getElementById('minSizeFilter').addEventListener('change', (e) => {
      this.settings.minSizeFilter = parseInt(e.target.value) || 0;
      this.saveSettings();
      this.applyFilters();
      this.renderMediaList();
    });
  }

  // 获取当前标签页
  async getCurrentTab() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      this.currentTab = tab;
      this.updatePageInfo();
    } catch (error) {
      console.error('Failed to get current tab:', error);
    }
  }

  // 更新页面信息
  updatePageInfo() {
    if (this.currentTab) {
      document.getElementById('pageTitle').textContent = this.currentTab.title || '未知页面';
      document.getElementById('pageUrl').textContent = this.currentTab.url || '';
    }
  }

  // 加载媒体项目
  async loadMediaItems() {
    console.log('PopupManager: 开始加载媒体项目...');
    this.showLoading();

    try {
      if (!this.currentTab) {
        console.log('PopupManager: 获取当前标签页...');
        await this.getCurrentTab();
      }

      console.log('PopupManager: 发送getMediaItems消息到标签页:', this.currentTab.id);
      const response = await chrome.tabs.sendMessage(this.currentTab.id, {
        action: 'getMediaItems'
      });

      console.log('PopupManager: 收到响应:', response);

      if (response && response.mediaItems) {
        console.log(`PopupManager: 收到 ${response.mediaItems.length} 个媒体项目`);
        this.mediaItems = response.mediaItems;
        this.applyFilters();
        this.updateStats();
        this.renderMediaList();
      } else {
        console.log('PopupManager: 没有收到有效的媒体项目，显示空状态');
        this.showEmptyState();
      }
    } catch (error) {
      console.error('PopupManager: 加载媒体项目失败:', error);
      this.showEmptyState();
    }
  }

  // 刷新媒体项目
  async refreshMediaItems() {
    console.log('PopupManager: 开始刷新媒体项目...');
    this.showLoading();

    try {
      console.log('PopupManager: 发送rescanMedia消息到标签页:', this.currentTab.id);
      const response = await chrome.tabs.sendMessage(this.currentTab.id, {
        action: 'rescanMedia'
      });

      console.log('PopupManager: 收到响应:', response);

      if (response && response.mediaItems) {
        console.log(`PopupManager: 收到 ${response.mediaItems.length} 个媒体项目`);
        this.mediaItems = response.mediaItems;
        this.selectedItems.clear();
        this.applyFilters();
        this.updateStats();
        this.renderMediaList();
      } else {
        console.log('PopupManager: 没有收到有效的媒体项目，显示空状态');
        this.showEmptyState();
      }
    } catch (error) {
      console.error('PopupManager: 刷新媒体项目失败:', error);
      this.showEmptyState();
    }
  }

  // 应用过滤器
  applyFilters() {
    console.log('PopupManager: 开始应用过滤器...');
    console.log('PopupManager: 原始媒体项目数量:', this.mediaItems.length);

    const typeFilter = document.getElementById('typeFilter')?.value || 'all';
    const sizeFilter = document.getElementById('sizeFilter')?.value || 'all';
    const searchQuery = document.getElementById('searchInput')?.value?.toLowerCase() || '';

    console.log('PopupManager: 过滤条件 - 类型:', typeFilter, '大小:', sizeFilter, '搜索:', searchQuery);

    this.filteredItems = this.mediaItems.filter(item => {
      // 类型过滤
      if (typeFilter !== 'all' && item.type !== typeFilter) {
        return false;
      }

      // 大小过滤
      if (sizeFilter !== 'all') {
        const size = item.size || 0;
        switch (sizeFilter) {
          case 'small':
            if (size >= 100 * 1024) return false;
            break;
          case 'medium':
            if (size < 100 * 1024 || size >= 1024 * 1024) return false;
            break;
          case 'large':
            if (size < 1024 * 1024) return false;
            break;
        }
      }

      // 最小大小过滤
      if (this.settings.minSizeFilter > 0) {
        const size = item.size || 0;
        if (size < this.settings.minSizeFilter * 1024) return false;
      }

      // 搜索过滤
      if (searchQuery) {
        const searchText = `${item.url} ${item.domain} ${item.alt || ''}`.toLowerCase();
        if (!searchText.includes(searchQuery)) return false;
      }

      return true;
    });

    console.log('PopupManager: 过滤后的媒体项目数量:', this.filteredItems.length);
    console.log('PopupManager: 过滤后的媒体项目:', this.filteredItems);
  }

  // 更新统计信息
  updateStats() {
    const imageCount = this.mediaItems.filter(item => item.type === 'image').length;
    const videoCount = this.mediaItems.filter(item => item.type === 'video').length;
    const totalCount = this.mediaItems.length;

    document.getElementById('imageCount').textContent = imageCount;
    document.getElementById('videoCount').textContent = videoCount;
    document.getElementById('totalCount').textContent = totalCount;
    
    this.updateSelectedCount();
  }

  // 更新选中数量
  updateSelectedCount() {
    const selectedCountElement = document.getElementById('selectedCount');
    console.log('updateSelectedCount - selectedItems.size:', this.selectedItems.size);
    console.log('updateSelectedCount - selectedCountElement:', selectedCountElement);

    if (selectedCountElement) {
      selectedCountElement.textContent = `已选择: ${this.selectedItems.size}`;
    } else {
      console.error('找不到selectedCount元素');
    }
  }

  // 显示加载状态
  showLoading() {
    const loadingIndicator = document.getElementById('loadingIndicator');
    const emptyState = document.getElementById('emptyState');
    const mediaList = document.getElementById('mediaList');

    console.log('showLoading - 元素检查:', {
      loadingIndicator: !!loadingIndicator,
      emptyState: !!emptyState,
      mediaList: !!mediaList
    });

    if (loadingIndicator) loadingIndicator.style.display = 'flex';
    if (emptyState) emptyState.style.display = 'none';
    if (mediaList) mediaList.innerHTML = '';
  }

  // 显示空状态
  showEmptyState() {
    const loadingIndicator = document.getElementById('loadingIndicator');
    const emptyState = document.getElementById('emptyState');

    console.log('showEmptyState - 元素检查:', {
      loadingIndicator: !!loadingIndicator,
      emptyState: !!emptyState
    });

    if (loadingIndicator) loadingIndicator.style.display = 'none';
    if (emptyState) emptyState.style.display = 'block';
  }

  // 隐藏加载和空状态
  hideLoadingAndEmpty() {
    const loadingIndicator = document.getElementById('loadingIndicator');
    const emptyState = document.getElementById('emptyState');

    console.log('hideLoadingAndEmpty - 元素检查:', {
      loadingIndicator: !!loadingIndicator,
      emptyState: !!emptyState
    });

    if (loadingIndicator) loadingIndicator.style.display = 'none';
    if (emptyState) emptyState.style.display = 'none';
  }

  // 渲染媒体列表
  renderMediaList() {
    console.log('PopupManager: 开始渲染媒体列表...');
    console.log('PopupManager: 过滤后的项目数量:', this.filteredItems.length);

    this.hideLoadingAndEmpty();

    const mediaList = document.getElementById('mediaList');
    console.log('PopupManager: mediaList元素:', mediaList);

    if (this.filteredItems.length === 0) {
      console.log('PopupManager: 没有过滤后的项目，显示空状态');
      this.showEmptyState();
      return;
    }

    let html = '';

    if (this.settings.groupByDomainEnabled) {
      console.log('PopupManager: 使用域名分组渲染');
      html = this.renderGroupedByDomain();
    } else {
      console.log('PopupManager: 使用平铺列表渲染');
      html = this.renderFlatList();
    }

    console.log('PopupManager: 生成的HTML长度:', html.length);
    console.log('PopupManager: 生成的HTML预览:', html.substring(0, 200) + '...');

    mediaList.innerHTML = html;
    this.attachMediaItemListeners();

    console.log('PopupManager: 媒体列表渲染完成');
  }

  // 渲染平铺列表
  renderFlatList() {
    return this.filteredItems.map(item => this.createMediaItemHTML(item)).join('');
  }

  // 按域名分组渲染
  renderGroupedByDomain() {
    const groups = {};

    this.filteredItems.forEach(item => {
      const domain = item.domain || 'unknown';
      if (!groups[domain]) {
        groups[domain] = [];
      }
      groups[domain].push(item);
    });

    let html = '';
    Object.keys(groups).sort().forEach(domain => {
      html += `<div class="domain-group">
        <div class="domain-header">${domain} (${groups[domain].length})</div>
        ${groups[domain].map(item => this.createMediaItemHTML(item)).join('')}
      </div>`;
    });

    return html;
  }

  // 创建媒体项目HTML
  createMediaItemHTML(item) {
    const isSelected = this.selectedItems.has(item.id);
    const thumbnailSrc = this.settings.showThumbnailsEnabled && item.type === 'image' ? item.url : '';
    const size = this.formatFileSize(item.size);
    const dimensions = item.width && item.height ? `${item.width}×${item.height}` : '';

    return `
      <div class="media-item ${isSelected ? 'selected' : ''}" data-id="${item.id}">
        <input type="checkbox" class="media-checkbox" ${isSelected ? 'checked' : ''}>
        <img class="media-thumbnail" src="${thumbnailSrc}" alt="缩略图"
             onerror="this.style.display='none'"
             style="${thumbnailSrc ? '' : 'display:none'}">
        <div class="media-info">
          <div class="media-title">
            ${this.getMediaTitle(item)}
            <span class="type-badge ${item.type}">${this.getTypeLabel(item.type)}</span>
          </div>
          <div class="media-details">
            ${size ? `<span>${size}</span>` : ''}
            ${dimensions ? `<span>${dimensions}</span>` : ''}
            <span>${item.domain}</span>
          </div>
          <div class="media-url" title="${item.url}">${item.url}</div>
        </div>
        <div class="media-actions">
          <button class="btn btn-small btn-outline download-btn" title="下载">⬇️</button>
          <button class="btn btn-small btn-outline highlight-btn" title="高亮">🎯</button>
          <button class="btn btn-small btn-outline copy-btn" title="复制">📋</button>
        </div>
      </div>
    `;
  }

  // 获取媒体标题
  getMediaTitle(item) {
    if (item.alt) return item.alt;

    try {
      const url = new URL(item.url);
      const filename = url.pathname.split('/').pop();
      return filename || 'Untitled';
    } catch {
      return 'Untitled';
    }
  }

  // 获取类型标签
  getTypeLabel(type) {
    const labels = {
      'image': '图片',
      'video': '视频',
      'background-image': '背景图'
    };
    return labels[type] || type;
  }

  // 格式化文件大小
  formatFileSize(bytes) {
    if (!bytes) return '';

    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  // 附加媒体项目事件监听器
  attachMediaItemListeners() {
    // 复选框事件
    document.querySelectorAll('.media-checkbox').forEach(checkbox => {
      checkbox.addEventListener('change', (e) => {
        const mediaItem = e.target.closest('.media-item');
        const itemId = mediaItem.dataset.id;

        if (e.target.checked) {
          this.selectedItems.add(itemId);
          mediaItem.classList.add('selected');
        } else {
          this.selectedItems.delete(itemId);
          mediaItem.classList.remove('selected');
        }

        this.updateSelectedCount();
      });
    });

    // 下载按钮事件
    document.querySelectorAll('.download-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const mediaItem = e.target.closest('.media-item');
        const itemId = mediaItem.dataset.id;
        this.downloadMediaItem(itemId);
      });
    });

    // 高亮按钮事件
    document.querySelectorAll('.highlight-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const mediaItem = e.target.closest('.media-item');
        const itemId = mediaItem.dataset.id;
        this.highlightMediaItem(itemId);
      });
    });

    // 复制按钮事件
    document.querySelectorAll('.copy-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const mediaItem = e.target.closest('.media-item');
        const itemId = mediaItem.dataset.id;
        this.copyMediaUrl(itemId);
      });
    });
  }

  // 全选
  selectAll() {
    console.log('selectAll - 开始全选');
    console.log('selectAll - filteredItems数量:', this.filteredItems.length);

    this.filteredItems.forEach(item => {
      this.selectedItems.add(item.id);
    });

    console.log('selectAll - 选中项目数量:', this.selectedItems.size);
    this.updateSelectedCount();
    this.renderMediaList();
  }

  // 取消全选
  selectNone() {
    console.log('selectNone - 开始取消全选');
    console.log('selectNone - 当前选中数量:', this.selectedItems.size);

    this.selectedItems.clear();

    console.log('selectNone - 清除后选中数量:', this.selectedItems.size);
    this.updateSelectedCount();
    this.renderMediaList();
  }

  // 下载选中的项目
  async downloadSelected() {
    const selectedMediaItems = this.filteredItems.filter(item =>
      this.selectedItems.has(item.id)
    );

    if (selectedMediaItems.length === 0) {
      this.showToast('请先选择要下载的媒体文件', 'warning');
      return;
    }

    // 按域名分组显示下载信息
    const domainGroups = {};
    selectedMediaItems.forEach(item => {
      const domain = item.domain || 'unknown';
      if (!domainGroups[domain]) {
        domainGroups[domain] = 0;
      }
      domainGroups[domain]++;
    });

    const domainInfo = Object.entries(domainGroups)
      .map(([domain, count]) => `${domain}: ${count}个`)
      .join('\n');

    const confirmMessage = `准备下载 ${selectedMediaItems.length} 个文件到以下文件夹：\n\n${domainInfo}\n\n确认下载？`;

    if (!confirm(confirmMessage)) {
      return;
    }

    try {
      this.showToast(`开始下载 ${selectedMediaItems.length} 个文件...`, 'info');

      const downloadItems = selectedMediaItems.map(item => ({
        url: item.url,
        filename: this.generateFilename(item)
      }));

      const response = await chrome.runtime.sendMessage({
        action: 'downloadBatch',
        items: downloadItems
      });

      if (response.success) {
        const successCount = response.results.filter(r => r.success).length;
        const failCount = response.results.length - successCount;

        if (failCount === 0) {
          this.showToast(`✅ 成功下载 ${successCount} 个文件`, 'success');
        } else {
          this.showToast(`⚠️ 下载完成：${successCount} 成功，${failCount} 失败`, 'warning');
        }
      } else {
        this.showToast(`❌ 下载失败: ${response.error}`, 'error');
      }
    } catch (error) {
      console.error('Download failed:', error);
      this.showToast('❌ 下载失败，请重试', 'error');
    }
  }

  // 下载单个媒体项目
  async downloadMediaItem(itemId) {
    const item = this.mediaItems.find(item => item.id === itemId);
    if (!item) return;

    try {
      const filename = this.generateFilename(item);
      const response = await chrome.runtime.sendMessage({
        action: 'downloadMedia',
        url: item.url,
        filename: filename
      });

      if (response.success) {
        const domain = item.domain || 'unknown';
        this.showToast(`📥 开始下载到 ${domain} 文件夹`, 'success');
        console.log('Download started:', item.url);
      } else {
        this.showToast(`❌ 下载失败: ${response.error}`, 'error');
      }
    } catch (error) {
      console.error('Download failed:', error);
      this.showToast('❌ 下载失败，请重试', 'error');
    }
  }

  // 高亮媒体项目
  async highlightMediaItem(itemId) {
    try {
      await chrome.tabs.sendMessage(this.currentTab.id, {
        action: 'highlightElement',
        id: itemId
      });
    } catch (error) {
      console.error('Highlight failed:', error);
    }
  }

  // 复制媒体URL
  async copyMediaUrl(itemId) {
    const item = this.mediaItems.find(item => item.id === itemId);
    if (!item) return;

    try {
      await navigator.clipboard.writeText(item.url);
      // 显示复制成功的提示
      this.showToast('📋 链接已复制到剪贴板', 'success');
    } catch (error) {
      console.error('Copy failed:', error);
      // 降级方案
      try {
        const textArea = document.createElement('textarea');
        textArea.value = item.url;
        textArea.style.position = 'fixed';
        textArea.style.opacity = '0';
        document.body.appendChild(textArea);
        textArea.select();
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
          this.showToast('📋 链接已复制到剪贴板', 'success');
        } else {
          this.showToast('❌ 复制失败，请手动复制', 'error');
        }
      } catch (fallbackError) {
        console.error('Fallback copy failed:', fallbackError);
        this.showToast('❌ 复制失败，请手动复制', 'error');
      }
    }
  }

  // 生成文件名（包含文件夹路径）
  generateFilename(item) {
    try {
      const url = new URL(item.url);
      const domain = this.sanitizeFolderName(url.hostname);
      const pathname = url.pathname;
      const filename = pathname.split('/').pop();

      let finalFilename;
      if (filename && filename.includes('.')) {
        finalFilename = this.sanitizeFilename(filename);
      } else {
        const extension = this.guessExtension(item);
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        finalFilename = `media_${timestamp}${extension}`;
      }

      // 返回包含文件夹的完整路径
      return `Vision Sniffer/${domain}/${finalFilename}`;
    } catch (error) {
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      return `Vision Sniffer/unknown/media_${timestamp}`;
    }
  }

  // 清理文件夹名称，移除不合法字符
  sanitizeFolderName(name) {
    return name
      .replace(/[<>:"/\\|?*]/g, '_')  // 替换不合法字符
      .replace(/^www\./, '')          // 移除www前缀
      .replace(/\.$/, '')             // 移除末尾的点
      .substring(0, 50);              // 限制长度
  }

  // 清理文件名，移除不合法字符
  sanitizeFilename(filename) {
    return filename
      .replace(/[<>:"/\\|?*]/g, '_')  // 替换不合法字符
      .substring(0, 100);             // 限制长度
  }

  // 猜测文件扩展名
  guessExtension(item) {
    const url = item.url.toLowerCase();

    if (item.type === 'video') {
      if (url.includes('.mp4')) return '.mp4';
      if (url.includes('.webm')) return '.webm';
      if (url.includes('.ogg')) return '.ogg';
      return '.mp4';
    } else {
      if (url.includes('.jpg') || url.includes('.jpeg')) return '.jpg';
      if (url.includes('.png')) return '.png';
      if (url.includes('.gif')) return '.gif';
      if (url.includes('.webp')) return '.webp';
      if (url.includes('.svg')) return '.svg';
      return '.jpg';
    }
  }

  // 切换设置面板
  toggleSettingsPanel() {
    const panel = document.getElementById('settingsPanel');
    const isVisible = panel.classList.contains('show');

    console.log('PopupManager: 切换设置面板，当前状态:', isVisible ? '显示' : '隐藏');

    if (isVisible) {
      // 隐藏设置面板
      panel.classList.remove('show');
      // 延迟隐藏，等待动画完成
      setTimeout(() => {
        panel.style.display = 'none';
      }, 300);
      console.log('PopupManager: 隐藏设置面板');
    } else {
      // 显示设置面板
      panel.style.display = 'block';
      // 强制重绘后添加show类以触发动画
      requestAnimationFrame(() => {
        panel.classList.add('show');
      });
      console.log('PopupManager: 显示设置面板');
    }
  }

  // 显示提示消息
  showToast(message, type = 'info') {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;

    // 根据类型设置样式
    const colors = {
      success: { bg: '#28a745', border: '#1e7e34' },
      error: { bg: '#dc3545', border: '#c82333' },
      warning: { bg: '#ffc107', border: '#e0a800', text: '#212529' },
      info: { bg: '#17a2b8', border: '#138496' }
    };

    const color = colors[type] || colors.info;

    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${color.bg};
      color: ${color.text || 'white'};
      padding: 12px 20px;
      border-radius: 8px;
      border-left: 4px solid ${color.border};
      font-size: 13px;
      font-weight: 500;
      z-index: 10000;
      opacity: 0;
      transform: translateX(100%);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      max-width: 300px;
      word-wrap: break-word;
    `;

    document.body.appendChild(toast);

    // 显示动画
    setTimeout(() => {
      toast.style.opacity = '1';
      toast.style.transform = 'translateX(0)';
    }, 100);

    // 自动隐藏
    const duration = type === 'error' ? 4000 : 3000;
    setTimeout(() => {
      toast.style.opacity = '0';
      toast.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    }, duration);
  }
}

// 测试函数已移除

// 直接测试函数已移除

// 测试函数已移除

// 初始化弹出窗口管理器
document.addEventListener('DOMContentLoaded', () => {
  console.log('=== DOM加载完成 ===');

  // 初始化PopupManager
  try {
    console.log('开始初始化PopupManager...');
    const popupManager = new PopupManager();
    window.popupManagerInstance = popupManager;
    console.log('PopupManager初始化成功');
  } catch (error) {
    console.error('PopupManager初始化失败:', error);
  }
});
