<!DOCTYPE html>
<html>
<head>
    <title>Generate Icons</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        .icon-container { display: inline-block; margin: 10px; text-align: center; }
    </style>
</head>
<body>
    <h1>Vision Sniffer Icon Generator</h1>
    <p>Right-click on each canvas and "Save image as..." to save the icons.</p>
    
    <div class="icon-container">
        <canvas id="icon16" width="16" height="16"></canvas>
        <div>16x16</div>
    </div>
    
    <div class="icon-container">
        <canvas id="icon32" width="32" height="32"></canvas>
        <div>32x32</div>
    </div>
    
    <div class="icon-container">
        <canvas id="icon48" width="48" height="48"></canvas>
        <div>48x48</div>
    </div>
    
    <div class="icon-container">
        <canvas id="icon128" width="128" height="128"></canvas>
        <div>128x128</div>
    </div>

    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const center = size / 2;
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // 绘制背景圆形
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(center, center, center - 2, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制白色边框
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = Math.max(1, size / 32);
            ctx.stroke();
            
            // 绘制相机镜头
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = Math.max(1, size / 42);
            ctx.beginPath();
            ctx.arc(center, center, size * 0.27, 0, 2 * Math.PI);
            ctx.stroke();
            
            ctx.lineWidth = Math.max(1, size / 64);
            ctx.beginPath();
            ctx.arc(center, center, size * 0.2, 0, 2 * Math.PI);
            ctx.stroke();
            
            // 绘制中心光点
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.beginPath();
            ctx.arc(center, center, size * 0.12, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制播放按钮
            if (size >= 32) {
                ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
                ctx.beginPath();
                const playSize = size * 0.15;
                ctx.moveTo(center - playSize, center - playSize);
                ctx.lineTo(center - playSize, center + playSize);
                ctx.lineTo(center + playSize, center);
                ctx.closePath();
                ctx.fill();
            }
            
            // 绘制搜索图标
            if (size >= 48) {
                const searchX = center + size * 0.2;
                const searchY = center - size * 0.2;
                const searchRadius = size * 0.09;
                
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = Math.max(1, size / 42);
                ctx.beginPath();
                ctx.arc(searchX, searchY, searchRadius, 0, 2 * Math.PI);
                ctx.stroke();
                
                // 搜索柄
                const handleLength = size * 0.07;
                const angle = Math.PI / 4;
                ctx.beginPath();
                ctx.moveTo(searchX + searchRadius * Math.cos(angle), searchY + searchRadius * Math.sin(angle));
                ctx.lineTo(searchX + (searchRadius + handleLength) * Math.cos(angle), 
                          searchY + (searchRadius + handleLength) * Math.sin(angle));
                ctx.stroke();
            }
        }
        
        // 生成所有尺寸的图标
        drawIcon(document.getElementById('icon16'), 16);
        drawIcon(document.getElementById('icon32'), 32);
        drawIcon(document.getElementById('icon48'), 48);
        drawIcon(document.getElementById('icon128'), 128);
        
        // 自动下载功能
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 添加下载按钮
        const downloadBtn = document.createElement('button');
        downloadBtn.textContent = 'Download All Icons';
        downloadBtn.style.cssText = 'padding: 10px 20px; font-size: 16px; margin: 20px 0;';
        downloadBtn.onclick = function() {
            downloadCanvas(document.getElementById('icon16'), 'icon16.png');
            setTimeout(() => downloadCanvas(document.getElementById('icon32'), 'icon32.png'), 100);
            setTimeout(() => downloadCanvas(document.getElementById('icon48'), 'icon48.png'), 200);
            setTimeout(() => downloadCanvas(document.getElementById('icon128'), 'icon128.png'), 300);
        };
        document.body.appendChild(downloadBtn);
    </script>
</body>
</html>
