# Vision Sniffer 调试指南

## 🔧 问题诊断步骤

### 1. 安装插件
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `visionSniffer` 文件夹
6. 确认插件已成功加载且已启用

### 2. 检查插件状态
- 在扩展程序页面确认插件状态为"已启用"
- 浏览器工具栏应该出现Vision Sniffer图标
- 如果没有图标，检查是否需要点击拼图图标来显示

### 3. 测试基本功能

#### 步骤A: 打开测试页面
```
file:///Users/<USER>/Downloads/我自己的代码/visionSniffer/simple_test.html
```

#### 步骤B: 打开开发者工具
1. 按 `F12` 或右键选择"检查"
2. 切换到"Console"标签页
3. 查看是否有错误信息

#### 步骤C: 测试插件
1. 点击浏览器工具栏中的Vision Sniffer图标
2. 观察弹窗是否正常显示
3. 点击"刷新"按钮
4. 查看控制台日志

### 4. 查看调试日志

#### Content Script日志
在页面的控制台中查找以下日志：
```
Vision Sniffer: 开始扫描媒体文件...
Vision Sniffer: 找到 X 个img元素
Vision Sniffer: 检查图片 1: [URL]
Vision Sniffer: 添加有效图片: [URL]
Vision Sniffer: 扫描完成，总共发现 X 个媒体文件
```

#### Popup Script日志
在插件弹窗的控制台中查找以下日志：
```
PopupManager: 开始刷新媒体项目...
PopupManager: 发送rescanMedia消息到标签页: [TAB_ID]
PopupManager: 收到响应: [RESPONSE]
PopupManager: 收到 X 个媒体项目
```

### 5. 常见问题及解决方案

#### 问题1: 插件图标不显示
**可能原因:**
- 图标文件缺失
- manifest.json配置错误

**解决方案:**
1. 检查 `icons/` 文件夹是否存在
2. 生成图标文件：打开 `generate_icons.html`，点击"Download All Icons"
3. 将下载的PNG文件放入 `icons/` 文件夹
4. 重新加载插件

#### 问题2: 点击插件图标没有反应
**可能原因:**
- popup.html文件路径错误
- JavaScript错误

**解决方案:**
1. 检查浏览器控制台是否有错误
2. 确认 `popup.html`, `popup.css`, `popup.js` 文件存在
3. 重新加载插件

#### 问题3: 刷新按钮没有识别到媒体文件
**可能原因:**
- Content script未正确注入
- 消息传递失败
- URL验证过于严格

**调试步骤:**
1. 打开页面控制台，查看是否有Vision Sniffer的日志
2. 检查是否有JavaScript错误
3. 确认页面中确实有图片或视频元素
4. 查看插件弹窗的控制台日志

#### 问题4: 显示"未发现媒体文件"
**可能原因:**
- 图片URL不符合验证条件
- 扫描函数执行失败

**解决方案:**
1. 检查图片URL格式（应该是http/https开头）
2. 查看控制台日志，确认扫描过程
3. 尝试在不同的网站上测试

### 6. 手动测试步骤

#### 测试1: 基本图片识别
1. 打开 `simple_test.html`
2. 打开开发者工具控制台
3. 点击插件图标
4. 应该看到3个测试图片被识别

#### 测试2: 视频识别
1. 确认测试页面有视频元素
2. 点击插件的刷新按钮
3. 应该看到视频被识别

#### 测试3: 背景图片识别
1. 测试页面包含CSS背景图片
2. 刷新插件
3. 应该看到背景图片被识别

### 7. 高级调试

#### 查看插件后台页面
1. 访问 `chrome://extensions/`
2. 找到Vision Sniffer插件
3. 点击"详细信息"
4. 点击"检查视图" -> "Service Worker"
5. 查看后台脚本的控制台

#### 检查权限
确认插件具有以下权限：
- activeTab
- downloads  
- storage
- host_permissions: <all_urls>

#### 重置插件
如果问题持续：
1. 在扩展程序页面点击"移除"
2. 重新加载插件
3. 清除浏览器缓存
4. 重启浏览器

### 8. 报告问题

如果问题仍然存在，请提供以下信息：
1. Chrome版本号
2. 操作系统版本
3. 控制台错误日志
4. 测试页面URL
5. 插件行为描述

### 9. 成功标志

插件正常工作时应该看到：
- ✅ 插件图标正常显示
- ✅ 点击图标弹出窗口
- ✅ 控制台有扫描日志
- ✅ 能识别到测试页面的媒体文件
- ✅ 刷新功能正常工作
- ✅ 下载功能可用

---

**记住：** 每次修改代码后都需要在扩展程序页面点击"重新加载"按钮！
