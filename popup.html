<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Vision Sniffer</title>
		<link rel="stylesheet" href="popup.css" />
	</head>
	<body>
		<div class="container">
			<!-- 头部 -->
			<header class="header">
				<div class="logo">
					<img src="icons/icon32.png" alt="Vision Sniffer" class="logo-icon" />
					<h1>Vision Sniffer</h1>
				</div>
				<div class="actions">
					<button id="refreshBtn" class="btn btn-icon" title="刷新扫描">
						<span class="icon">🔄</span>
					</button>
					<button id="settingsBtn" class="btn btn-icon" title="设置">
						<span class="icon">⚙️</span>
					</button>
				</div>
			</header>

			<!-- 统计信息 -->
			<div class="stats">
				<div class="stat-item">
					<span class="stat-label">图片:</span>
					<span id="imageCount" class="stat-value">0</span>
				</div>
				<div class="stat-item">
					<span class="stat-label">视频:</span>
					<span id="videoCount" class="stat-value">0</span>
				</div>
				<div class="stat-item">
					<span class="stat-label">总计:</span>
					<span id="totalCount" class="stat-value">0</span>
				</div>
			</div>

			<!-- 过滤器 -->
			<div class="filters">
				<div class="filter-group">
					<label for="typeFilter">类型:</label>
					<select id="typeFilter">
						<option value="all">全部</option>
						<option value="image">图片</option>
						<option value="video">视频</option>
						<option value="background-image">背景图</option>
					</select>
				</div>
				<div class="filter-group">
					<label for="sizeFilter">大小:</label>
					<select id="sizeFilter">
						<option value="all">全部</option>
						<option value="small">小 (&lt;100KB)</option>
						<option value="medium">中 (100KB-1MB)</option>
						<option value="large">大 (&gt;1MB)</option>
					</select>
				</div>
				<div class="filter-group">
					<input type="text" id="searchInput" placeholder="搜索URL或域名..." />
				</div>
			</div>

			<!-- 批量操作 -->
			<div class="batch-actions">
				<button id="selectAllBtn" class="btn btn-small">全选</button>
				<button id="selectNoneBtn" class="btn btn-small">取消全选</button>
				<button id="downloadSelectedBtn" class="btn btn-primary btn-small">下载选中</button>
				<span id="selectedCount" class="selected-info">已选择: 0</span>
			</div>

			<!-- 媒体列表 -->
			<div class="media-list" id="mediaList">
				<div class="loading" id="loadingIndicator">
					<div class="spinner"></div>
					<span>正在扫描媒体文件...</span>
				</div>
				<div class="empty-state" id="emptyState" style="display: none">
					<div class="empty-icon">📷</div>
					<p>未发现媒体文件</p>
					<button id="rescanBtn" class="btn btn-primary">重新扫描</button>
				</div>
			</div>

			<!-- 底部信息 -->
			<footer class="footer">
				<div class="page-info">
					<span id="pageTitle">当前页面</span>
					<span id="pageUrl" class="page-url"></span>
				</div>
			</footer>
		</div>

		<!-- 设置面板 -->
		<div class="settings-panel" id="settingsPanel" style="display: none">
			<div class="settings-header">
				<h3>设置</h3>
				<button id="closeSettingsBtn" class="btn btn-icon">✕</button>
			</div>
			<div class="settings-content">
				<div class="setting-item">
					<label>
						<input type="checkbox" id="autoScanEnabled" checked />
						自动扫描新加载的媒体
					</label>
				</div>
				<div class="setting-item">
					<label>
						<input type="checkbox" id="showThumbnailsEnabled" checked />
						显示缩略图
					</label>
				</div>
				<div class="setting-item">
					<label>
						<input type="checkbox" id="groupByDomainEnabled" />
						按域名分组
					</label>
				</div>
				<div class="setting-item">
					<label for="minSizeFilter">最小文件大小 (KB):</label>
					<input type="number" id="minSizeFilter" min="0" value="0" />
				</div>

				<!-- 返回按钮 -->
				<div class="setting-item" style="margin-top: 30px; text-align: center;">
					<button id="backToMainBtn" class="btn btn-primary" style="padding: 10px 30px;">
						← 返回主界面
					</button>
				</div>
			</div>
		</div>

		<script src="popup.js"></script>
	</body>
</html>
