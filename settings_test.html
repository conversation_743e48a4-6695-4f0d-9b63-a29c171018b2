<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置面板测试 - Vision Sniffer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #28a745;
            border-radius: 8px;
            background: #f8fff9;
        }
        
        .test-section h2 {
            color: #28a745;
            margin-top: 0;
        }
        
        .instructions {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .instructions h3 {
            color: #155724;
            margin-top: 0;
        }
        
        .test-steps {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .test-steps h3 {
            color: #856404;
            margin-top: 0;
        }
        
        .test-steps ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .test-steps li {
            margin: 8px 0;
        }
        
        .expected-result {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .expected-result h3 {
            color: #0c5460;
            margin-top: 0;
        }
        
        .image-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        
        .image-item {
            flex: 1;
            min-width: 200px;
            text-align: center;
        }
        
        .image-item img {
            max-width: 100%;
            height: 120px;
            object-fit: cover;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        
        .checklist {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .checklist h3 {
            color: #495057;
            margin-top: 0;
        }
        
        .checklist ul {
            list-style: none;
            padding: 0;
        }
        
        .checklist li {
            margin: 8px 0;
            padding: 5px 0;
        }
        
        .checklist li::before {
            content: "☐ ";
            font-size: 16px;
            margin-right: 8px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-good {
            background: #28a745;
        }
        
        .status-warning {
            background: #ffc107;
        }
        
        .status-error {
            background: #dc3545;
        }
    </style>
</head>
<body>
    <h1>🔧 Vision Sniffer 设置面板测试</h1>
    
    <div class="instructions">
        <h3>📋 测试目的</h3>
        <p>这个页面用于测试Vision Sniffer插件的设置面板功能，特别是设置面板的打开、关闭和返回功能。</p>
    </div>

    <div class="test-steps">
        <h3>🧪 测试步骤</h3>
        <ol>
            <li><strong>安装插件</strong>：确保Vision Sniffer插件已正确安装并启用</li>
            <li><strong>打开插件</strong>：点击浏览器工具栏中的插件图标</li>
            <li><strong>进入设置</strong>：点击插件弹窗右上角的设置按钮（⚙️）</li>
            <li><strong>测试返回方式1</strong>：点击设置面板右上角的红色关闭按钮（✕）</li>
            <li><strong>再次进入设置</strong>：重新点击设置按钮</li>
            <li><strong>测试返回方式2</strong>：点击设置面板底部的"← 返回主界面"按钮</li>
            <li><strong>再次进入设置</strong>：重新点击设置按钮</li>
            <li><strong>测试返回方式3</strong>：按键盘上的ESC键</li>
            <li><strong>测试设置功能</strong>：在设置面板中修改各项设置，确认保存正常</li>
        </ol>
    </div>

    <div class="expected-result">
        <h3>✅ 预期结果</h3>
        <ul>
            <li><span class="status-indicator status-good"></span>设置面板能正常打开，有滑入动画效果</li>
            <li><span class="status-indicator status-good"></span>三种返回方式都能正常工作：
                <ul style="margin-left: 20px; margin-top: 5px;">
                    <li>• 点击右上角关闭按钮（✕）</li>
                    <li>• 点击底部返回按钮</li>
                    <li>• 按ESC键</li>
                </ul>
            </li>
            <li><span class="status-indicator status-good"></span>返回时有滑出动画效果</li>
            <li><span class="status-indicator status-good"></span>设置项能正常修改和保存</li>
            <li><span class="status-indicator status-good"></span>设置面板完全覆盖主界面</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>📷 测试媒体内容</h2>
        <p>以下是一些测试图片，用于验证插件的基本识别功能：</p>
        <div class="image-container">
            <div class="image-item">
                <img src="https://via.placeholder.com/250x150/FF6B6B/FFFFFF?text=Settings+Test+1" alt="设置测试图片1">
                <p>设置测试图片 1</p>
            </div>
            <div class="image-item">
                <img src="https://via.placeholder.com/250x150/4ECDC4/FFFFFF?text=Settings+Test+2" alt="设置测试图片2">
                <p>设置测试图片 2</p>
            </div>
            <div class="image-item">
                <img src="https://via.placeholder.com/250x150/45B7D1/FFFFFF?text=Settings+Test+3" alt="设置测试图片3">
                <p>设置测试图片 3</p>
            </div>
        </div>
    </div>

    <div class="checklist">
        <h3>📝 测试检查清单</h3>
        <ul>
            <li>插件图标正常显示</li>
            <li>点击插件图标弹出主界面</li>
            <li>能识别到页面中的3张测试图片</li>
            <li>设置按钮（⚙️）可以点击</li>
            <li>设置面板正常打开</li>
            <li>设置面板有滑入动画</li>
            <li>关闭按钮（✕）能正常工作</li>
            <li>返回按钮能正常工作</li>
            <li>ESC键能关闭设置面板</li>
            <li>设置面板有滑出动画</li>
            <li>各项设置能正常修改</li>
            <li>设置能正常保存</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🐛 故障排除</h2>
        <h3>如果设置面板无法正常返回：</h3>
        <ol>
            <li>打开浏览器开发者工具（F12）</li>
            <li>查看控制台是否有错误信息</li>
            <li>检查是否有JavaScript错误</li>
            <li>尝试重新加载插件</li>
            <li>重启浏览器后再次测试</li>
        </ol>
        
        <h3>常见问题：</h3>
        <ul>
            <li><strong>设置面板不显示</strong>：检查CSS文件是否正确加载</li>
            <li><strong>按钮无响应</strong>：检查JavaScript事件监听器是否正确绑定</li>
            <li><strong>动画不流畅</strong>：检查CSS过渡效果是否正确设置</li>
            <li><strong>ESC键无效</strong>：检查键盘事件监听器是否正确添加</li>
        </ul>
    </div>

    <script>
        // 添加一些调试信息
        console.log('设置面板测试页面已加载');
        console.log('页面包含的图片:', document.querySelectorAll('img').length);
        
        // 模拟用户交互测试
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F1') {
                console.log('F1键被按下 - 显示帮助信息');
                alert('设置面板测试帮助：\n1. 确保插件已安装\n2. 点击插件图标\n3. 测试设置面板的各种返回方式');
            }
        });
        
        // 页面加载完成提示
        window.addEventListener('load', () => {
            console.log('设置面板测试页面完全加载完成');
        });
    </script>
</body>
</html>
