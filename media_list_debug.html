<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>媒体列表调试 - Vision Sniffer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .debug-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #dc3545;
            border-radius: 8px;
            background: #fff5f5;
        }
        
        .debug-section h2 {
            color: #dc3545;
            margin-top: 0;
        }
        
        .instructions {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .instructions h3 {
            color: #721c24;
            margin-top: 0;
        }
        
        .test-images {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .test-image {
            text-align: center;
            padding: 10px;
            border: 2px solid #007bff;
            border-radius: 8px;
            background: #f8f9ff;
        }
        
        .test-image img {
            max-width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 4px;
        }
        
        .test-image h4 {
            margin: 10px 0 5px 0;
            color: #007bff;
        }
        
        .test-image p {
            margin: 5px 0;
            font-size: 12px;
            color: #666;
        }
        
        .debug-steps {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .debug-steps h3 {
            color: #856404;
            margin-top: 0;
        }
        
        .debug-steps ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .debug-steps li {
            margin: 8px 0;
            font-weight: 500;
        }
        
        .console-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 13px;
        }
        
        .console-info h3 {
            color: #0c5460;
            margin-top: 0;
            font-family: Arial, sans-serif;
        }
        
        .expected-logs {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .expected-logs h3 {
            color: #155724;
            margin-top: 0;
        }
        
        .expected-logs code {
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .video-test {
            margin: 20px 0;
            text-align: center;
        }
        
        .video-test video {
            max-width: 100%;
            height: 200px;
            border: 2px solid #28a745;
            border-radius: 8px;
        }
        
        .background-test {
            height: 150px;
            background-size: cover;
            background-position: center;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🐛 Vision Sniffer 媒体列表调试页面</h1>
    
    <div class="instructions">
        <h3>🎯 调试目标</h3>
        <p>这个页面专门用于调试Vision Sniffer插件的媒体列表显示问题。页面包含多种类型的媒体文件，用于测试插件的识别和显示功能。</p>
    </div>

    <div class="debug-steps">
        <h3>🔍 调试步骤</h3>
        <ol>
            <li><strong>重新加载插件</strong>：访问 chrome://extensions/，找到Vision Sniffer，点击"重新加载"</li>
            <li><strong>打开开发者工具</strong>：按F12，切换到Console标签页</li>
            <li><strong>点击插件图标</strong>：观察控制台日志输出</li>
            <li><strong>检查扫描日志</strong>：查看content script的扫描日志</li>
            <li><strong>检查弹窗日志</strong>：右键点击插件弹窗，选择"检查"，查看弹窗的控制台</li>
            <li><strong>点击刷新按钮</strong>：在插件弹窗中点击刷新按钮，观察日志</li>
            <li><strong>检查过滤器</strong>：尝试修改过滤器设置，观察效果</li>
        </ol>
    </div>

    <div class="debug-section">
        <h2>📷 测试图片 (应该识别到6张)</h2>
        <div class="test-images">
            <div class="test-image">
                <img src="https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=Debug+Image+1" alt="调试图片1">
                <h4>调试图片 1</h4>
                <p>URL: via.placeholder.com</p>
                <p>格式: PNG</p>
            </div>
            <div class="test-image">
                <img src="https://via.placeholder.com/300x200/4ECDC4/FFFFFF?text=Debug+Image+2" alt="调试图片2">
                <h4>调试图片 2</h4>
                <p>URL: via.placeholder.com</p>
                <p>格式: PNG</p>
            </div>
            <div class="test-image">
                <img src="https://via.placeholder.com/300x200/45B7D1/FFFFFF?text=Debug+Image+3" alt="调试图片3">
                <h4>调试图片 3</h4>
                <p>URL: via.placeholder.com</p>
                <p>格式: PNG</p>
            </div>
            <div class="test-image">
                <img src="https://picsum.photos/300/200?random=1" alt="随机图片1">
                <h4>随机图片 1</h4>
                <p>URL: picsum.photos</p>
                <p>格式: JPEG</p>
            </div>
            <div class="test-image">
                <img src="https://picsum.photos/300/200?random=2" alt="随机图片2">
                <h4>随机图片 2</h4>
                <p>URL: picsum.photos</p>
                <p>格式: JPEG</p>
            </div>
            <div class="test-image">
                <img src="https://httpbin.org/image/png" alt="HTTPBin PNG">
                <h4>HTTPBin PNG</h4>
                <p>URL: httpbin.org</p>
                <p>格式: PNG</p>
            </div>
        </div>
    </div>

    <div class="debug-section">
        <h2>🎥 测试视频 (应该识别到1个)</h2>
        <div class="video-test">
            <video controls poster="https://via.placeholder.com/400x200/96CEB4/FFFFFF?text=Video+Poster">
                <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">
                <source src="https://www.w3schools.com/html/mov_bbb.ogg" type="video/ogg">
                您的浏览器不支持视频播放。
            </video>
            <p><strong>测试视频</strong> - 包含MP4和OGG两个source</p>
        </div>
    </div>

    <div class="debug-section">
        <h2>🖼️ 测试背景图片 (应该识别到2个)</h2>
        <div class="background-test" style="background-image: url('https://via.placeholder.com/800x150/FF9F43/FFFFFF?text=Background+Image+1');">
            CSS 背景图片 1
        </div>
        <div class="background-test" style="background-image: url('https://picsum.photos/800/150?random=10');">
            CSS 背景图片 2
        </div>
    </div>

    <div class="expected-logs">
        <h3>📋 预期的控制台日志</h3>
        <h4>页面控制台 (Content Script):</h4>
        <code>Vision Sniffer: 开始扫描媒体文件...</code><br>
        <code>Vision Sniffer: 找到 6 个img元素</code><br>
        <code>Vision Sniffer: 检查图片 1: https://via.placeholder.com/...</code><br>
        <code>Vision Sniffer: 添加有效图片: https://via.placeholder.com/...</code><br>
        <code>Vision Sniffer: 扫描图片完成，发现 6 个图片</code><br>
        <code>Vision Sniffer: 扫描视频完成，发现 2 个视频</code><br>
        <code>Vision Sniffer: 扫描背景图片完成，发现 2 个背景图片</code><br>
        <code>Vision Sniffer: 扫描完成，总共发现 10 个媒体文件</code><br>
        
        <h4>插件弹窗控制台 (Popup Script):</h4>
        <code>PopupManager: 开始加载媒体项目...</code><br>
        <code>PopupManager: 发送getMediaItems消息到标签页: [TAB_ID]</code><br>
        <code>PopupManager: 收到 10 个媒体项目</code><br>
        <code>PopupManager: 开始应用过滤器...</code><br>
        <code>PopupManager: 过滤后的媒体项目数量: 10</code><br>
        <code>PopupManager: 开始渲染媒体列表...</code><br>
        <code>PopupManager: 媒体列表渲染完成</code>
    </div>

    <div class="console-info">
        <h3>🔧 如何查看插件弹窗的控制台</h3>
        <p>1. 点击插件图标打开弹窗</p>
        <p>2. 右键点击弹窗内容区域</p>
        <p>3. 选择"检查" (Inspect)</p>
        <p>4. 在开发者工具中切换到Console标签页</p>
        <p>5. 点击插件弹窗中的刷新按钮，观察日志输出</p>
    </div>

    <div class="debug-section">
        <h2>🚨 常见问题排查</h2>
        <h3>如果媒体列表不显示：</h3>
        <ul>
            <li><strong>检查扫描日志</strong>：页面控制台应该有"Vision Sniffer: 扫描完成"的日志</li>
            <li><strong>检查消息传递</strong>：弹窗控制台应该有"收到X个媒体项目"的日志</li>
            <li><strong>检查过滤器</strong>：确认过滤条件没有过滤掉所有项目</li>
            <li><strong>检查HTML渲染</strong>：弹窗控制台应该有"生成的HTML长度"的日志</li>
            <li><strong>检查DOM元素</strong>：确认mediaList元素存在且可访问</li>
        </ul>
        
        <h3>如果扫描没有找到媒体文件：</h3>
        <ul>
            <li><strong>检查URL验证</strong>：确认isValidUrl函数正常工作</li>
            <li><strong>检查元素选择器</strong>：确认document.querySelectorAll正常工作</li>
            <li><strong>检查网络请求</strong>：某些图片可能加载失败</li>
        </ul>
    </div>

    <script>
        // 页面加载调试信息
        console.log('=== 媒体列表调试页面 ===');
        console.log('页面URL:', window.location.href);
        console.log('图片元素数量:', document.querySelectorAll('img').length);
        console.log('视频元素数量:', document.querySelectorAll('video').length);
        
        // 列出所有图片URL
        const images = document.querySelectorAll('img');
        images.forEach((img, index) => {
            console.log(`图片 ${index + 1}:`, img.src, '- Alt:', img.alt);
        });
        
        // 列出所有视频URL
        const videos = document.querySelectorAll('video');
        videos.forEach((video, index) => {
            console.log(`视频 ${index + 1}:`, video.src || '使用source元素');
            const sources = video.querySelectorAll('source');
            sources.forEach((source, sourceIndex) => {
                console.log(`  Source ${sourceIndex + 1}:`, source.src, '- Type:', source.type);
            });
        });
        
        // 检查背景图片
        let backgroundCount = 0;
        document.querySelectorAll('*').forEach(element => {
            const style = window.getComputedStyle(element);
            if (style.backgroundImage && style.backgroundImage !== 'none') {
                backgroundCount++;
                console.log(`背景图片 ${backgroundCount}:`, style.backgroundImage);
            }
        });
        
        console.log('=== 调试页面加载完成 ===');
        
        // 添加动态内容测试
        setTimeout(() => {
            console.log('添加动态测试图片...');
            const dynamicImg = document.createElement('img');
            dynamicImg.src = 'https://via.placeholder.com/200x150/E17055/FFFFFF?text=Dynamic+Test';
            dynamicImg.alt = '动态测试图片';
            dynamicImg.style.cssText = 'margin: 10px; border: 2px solid #E17055; border-radius: 8px;';
            
            const container = document.querySelector('.debug-section');
            container.appendChild(dynamicImg);
            
            console.log('动态图片已添加:', dynamicImg.src);
        }, 3000);
    </script>
</body>
</html>
